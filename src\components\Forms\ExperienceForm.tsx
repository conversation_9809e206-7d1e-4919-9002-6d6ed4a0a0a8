import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { useResumeStore } from '../../store/resumeStore';
import { Experience } from '../../types/resume';
import GrammarHighlighter from '../GrammarChecker/GrammarHighlighter';

const ExperienceForm: React.FC = () => {
  const { currentResume, updateSection } = useResumeStore();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  
  const experienceSection = currentResume?.sections.find(s => s.type === 'experience');
  const experiences = (experienceSection?.data || []) as Experience[];

  const { register, handleSubmit, reset, setValue, watch } = useForm<Experience>();
  const watchedDescription = watch('description');

  const handleSave = (data: Experience) => {
    const newExperiences = editingId
      ? experiences.map(exp => exp.id === editingId ? { ...data, id: editingId } : exp)
      : [...experiences, { ...data, id: uuidv4() }];
    
    if (experienceSection) {
      updateSection(experienceSection.id, newExperiences);
    }
    
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleEdit = (experience: Experience) => {
    reset(experience);
    setEditingId(experience.id);
    setIsAdding(false);
  };

  const handleDelete = (id: string) => {
    const newExperiences = experiences.filter(exp => exp.id !== id);
    if (experienceSection) {
      updateSection(experienceSection.id, newExperiences);
    }
  };

  const handleCancel = () => {
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleDescriptionChange = (text: string) => {
    setValue('description', text);
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Work Experience</h3>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add</span>
        </button>
      </div>

      {(isAdding || editingId) && (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <form onSubmit={handleSubmit(handleSave)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company *
                </label>
                <input
                  {...register('company', { required: true })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position *
                </label>
                <input
                  {...register('position', { required: true })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <input
                {...register('location')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date *
                </label>
                <input
                  {...register('startDate', { required: true })}
                  type="month"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  {...register('endDate')}
                  type="month"
                  disabled={watch('current')}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                />
              </div>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  {...register('current')}
                  type="checkbox"
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">I currently work here</span>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <GrammarHighlighter
                text={watchedDescription || ''}
                onChange={handleDescriptionChange}
                placeholder="Describe your responsibilities and achievements..."
                className="min-h-24"
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="flex items-center space-x-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-3">
        {experiences.map((experience) => (
          <div key={experience.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{experience.position}</h4>
                <p className="text-sm text-gray-600">{experience.company}</p>
                <p className="text-xs text-gray-500">
                  {experience.startDate} - {experience.current ? 'Present' : experience.endDate}
                  {experience.location && ` • ${experience.location}`}
                </p>
                {experience.description && (
                  <p className="text-sm text-gray-700 mt-2">{experience.description}</p>
                )}
              </div>
              <div className="flex space-x-1 ml-4">
                <button
                  onClick={() => handleEdit(experience)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(experience.id)}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExperienceForm;
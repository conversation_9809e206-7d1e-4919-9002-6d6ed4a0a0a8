import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { ResumeData, ResumeSection, PersonalInfo, Experience, Education, Skill, Project, Publication, CustomSection } from '../types/resume';

interface ResumeState {
  currentResume: ResumeData | null;
  history: ResumeData[];
  historyIndex: number;
  selectedTemplate: string;
  
  // Actions
  createNewResume: () => void;
  loadResume: (resume: ResumeData) => void;
  updatePersonalInfo: (info: Partial<PersonalInfo>) => void;
  addSection: (type: ResumeSection['type'], title: string) => void;
  updateSection: (sectionId: string, data: any) => void;
  removeSection: (sectionId: string) => void;
  reorderSections: (sections: ResumeSection[]) => void;
  toggleSectionVisibility: (sectionId: string) => void;
  setTemplate: (templateId: string) => void;
  undo: () => void;
  redo: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
}

const createEmptyResume = (): ResumeData => ({
  id: uuidv4(),
  title: 'My Resume',
  template: 'modern',
  personal: {
    id: uuidv4(),
    fullName: '',
    email: '',
    phone: '',
    address: '',
    linkedin: '',
    website: '',
    summary: ''
  },
  sections: [
    {
      id: uuidv4(),
      type: 'experience',
      title: 'Work Experience',
      visible: true,
      order: 1,
      data: [] as Experience[]
    },
    {
      id: uuidv4(),
      type: 'education',
      title: 'Education',
      visible: true,
      order: 2,
      data: [] as Education[]
    },
    {
      id: uuidv4(),
      type: 'skills',
      title: 'Skills',
      visible: true,
      order: 3,
      data: [] as Skill[]
    },
    {
      id: uuidv4(),
      type: 'projects',
      title: 'Projects',
      visible: true,
      order: 4,
      data: [] as Project[]
    }
  ],
  customSections: [],
  createdAt: new Date(),
  updatedAt: new Date()
});

export const useResumeStore = create<ResumeState>((set, get) => ({
  currentResume: createEmptyResume(),
  history: [],
  historyIndex: -1,
  selectedTemplate: 'modern',

  createNewResume: () => {
    const newResume = createEmptyResume();
    set({
      currentResume: newResume,
      history: [newResume],
      historyIndex: 0
    });
  },

  loadResume: (resume) => {
    set({
      currentResume: resume,
      history: [resume],
      historyIndex: 0
    });
  },

  updatePersonalInfo: (info) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedResume = {
      ...state.currentResume,
      personal: { ...state.currentResume.personal, ...info },
      updatedAt: new Date()
    };

    const newHistory = state.history.slice(0, state.historyIndex + 1);
    newHistory.push(updatedResume);

    set({
      currentResume: updatedResume,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });
  },

  addSection: (type, title) => {
    const state = get();
    if (!state.currentResume) return;

    const newSection: ResumeSection = {
      id: uuidv4(),
      type,
      title,
      visible: true,
      order: state.currentResume.sections.length + 1,
      data: []
    };

    const updatedResume = {
      ...state.currentResume,
      sections: [...state.currentResume.sections, newSection],
      updatedAt: new Date()
    };

    const newHistory = state.history.slice(0, state.historyIndex + 1);
    newHistory.push(updatedResume);

    set({
      currentResume: updatedResume,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });
  },

  updateSection: (sectionId, data) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedSections = state.currentResume.sections.map(section =>
      section.id === sectionId ? { ...section, data } : section
    );

    const updatedResume = {
      ...state.currentResume,
      sections: updatedSections,
      updatedAt: new Date()
    };

    const newHistory = state.history.slice(0, state.historyIndex + 1);
    newHistory.push(updatedResume);

    set({
      currentResume: updatedResume,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });
  },

  removeSection: (sectionId) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedSections = state.currentResume.sections.filter(
      section => section.id !== sectionId
    );

    const updatedResume = {
      ...state.currentResume,
      sections: updatedSections,
      updatedAt: new Date()
    };

    const newHistory = state.history.slice(0, state.historyIndex + 1);
    newHistory.push(updatedResume);

    set({
      currentResume: updatedResume,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });
  },

  reorderSections: (sections) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedResume = {
      ...state.currentResume,
      sections: sections.map((section, index) => ({ ...section, order: index })),
      updatedAt: new Date()
    };

    const newHistory = state.history.slice(0, state.historyIndex + 1);
    newHistory.push(updatedResume);

    set({
      currentResume: updatedResume,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });
  },

  toggleSectionVisibility: (sectionId) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedSections = state.currentResume.sections.map(section =>
      section.id === sectionId ? { ...section, visible: !section.visible } : section
    );

    const updatedResume = {
      ...state.currentResume,
      sections: updatedSections,
      updatedAt: new Date()
    };

    set({ currentResume: updatedResume });
  },

  setTemplate: (templateId) => {
    const state = get();
    if (!state.currentResume) return;

    const updatedResume = {
      ...state.currentResume,
      template: templateId,
      updatedAt: new Date()
    };

    set({
      currentResume: updatedResume,
      selectedTemplate: templateId
    });
  },

  undo: () => {
    const state = get();
    if (state.historyIndex > 0) {
      set({
        currentResume: state.history[state.historyIndex - 1],
        historyIndex: state.historyIndex - 1
      });
    }
  },

  redo: () => {
    const state = get();
    if (state.historyIndex < state.history.length - 1) {
      set({
        currentResume: state.history[state.historyIndex + 1],
        historyIndex: state.historyIndex + 1
      });
    }
  },

  canUndo: () => {
    return get().historyIndex > 0;
  },

  canRedo: () => {
    const state = get();
    return state.historyIndex < state.history.length - 1;
  }
}));
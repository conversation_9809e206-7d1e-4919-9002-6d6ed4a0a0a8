export interface PersonalInfo {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  linkedin?: string;
  website?: string;
  summary: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  location: string;
  startDate: string;
  endDate: string;
  current: boolean;
  description: string;
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  location: string;
  startDate: string;
  endDate: string;
  current: boolean;
  gpa?: string;
  description?: string;
}

export interface Skill {
  id: string;
  name: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  category: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  url?: string;
  startDate: string;
  endDate?: string;
}

export interface Publication {
  id: string;
  title: string;
  authors: string;
  journal: string;
  year: string;
  url?: string;
  description?: string;
}

export interface CustomSection {
  id: string;
  title: string;
  fields: CustomField[];
  items: CustomSectionItem[];
}

export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'date' | 'url';
  label: string;
  required: boolean;
}

export interface CustomSectionItem {
  id: string;
  [key: string]: any;
}

export interface ResumeSection {
  id: string;
  type: 'personal' | 'experience' | 'education' | 'skills' | 'projects' | 'publications' | 'custom';
  title: string;
  visible: boolean;
  order: number;
  data: any;
}

export interface ResumeData {
  id: string;
  title: string;
  template: string;
  personal: PersonalInfo;
  sections: ResumeSection[];
  customSections: CustomSection[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  sections: string[];
  styling: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    headerStyle: string;
  };
}
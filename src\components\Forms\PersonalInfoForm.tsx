import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useResumeStore } from '../../store/resumeStore';
import GrammarHighlighter from '../GrammarChecker/GrammarHighlighter';

const schema = yup.object({
  fullName: yup.string().required('Full name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().required('Phone number is required'),
  address: yup.string().required('Address is required'),
  linkedin: yup.string().url('Invalid URL').optional(),
  website: yup.string().url('Invalid URL').optional(),
  summary: yup.string().optional()
});

const PersonalInfoForm: React.FC = () => {
  const { currentResume, updatePersonalInfo } = useResumeStore();
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: currentResume?.personal || {}
  });

  const watchedSummary = watch('summary');

  const onSubmit = (data: any) => {
    updatePersonalInfo(data);
  };

  const handleSummaryChange = (text: string) => {
    setValue('summary', text);
    updatePersonalInfo({ summary: text });
  };

  return (
    <div className="p-4 h-full overflow-y-auto">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            {...register('fullName')}
            type="text"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm break-words"
            onChange={(e) => {
              register('fullName').onChange(e);
              updatePersonalInfo({ fullName: e.target.value });
            }}
          />
          {errors.fullName && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.fullName.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email *
          </label>
          <input
            {...register('email')}
            type="email"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm break-all"
            onChange={(e) => {
              register('email').onChange(e);
              updatePersonalInfo({ email: e.target.value });
            }}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.email.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone *
          </label>
          <input
            {...register('phone')}
            type="tel"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            onChange={(e) => {
              register('phone').onChange(e);
              updatePersonalInfo({ phone: e.target.value });
            }}
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Address *
          </label>
          <textarea
            {...register('address')}
            rows={2}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm resize-none"
            onChange={(e) => {
              register('address').onChange(e);
              updatePersonalInfo({ address: e.target.value });
            }}
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.address.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            LinkedIn Profile
          </label>
          <input
            {...register('linkedin')}
            type="url"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm break-all"
            placeholder="https://linkedin.com/in/yourprofile"
            onChange={(e) => {
              register('linkedin').onChange(e);
              updatePersonalInfo({ linkedin: e.target.value });
            }}
          />
          {errors.linkedin && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.linkedin.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website
          </label>
          <input
            {...register('website')}
            type="url"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm break-all"
            placeholder="https://yourwebsite.com"
            onChange={(e) => {
              register('website').onChange(e);
              updatePersonalInfo({ website: e.target.value });
            }}
          />
          {errors.website && (
            <p className="mt-1 text-sm text-red-600 break-words">{errors.website.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Professional Summary
          </label>
          <GrammarHighlighter
            text={watchedSummary || ''}
            onChange={handleSummaryChange}
            placeholder="Write a brief professional summary..."
            className="min-h-24"
          />
          {errors.summary && (
            <p className="mt-1 text-sm text-red-600">{errors.summary.message}</p>
          )}
        </div>
      </form>
    </div>
  );
};

export default PersonalInfoForm;
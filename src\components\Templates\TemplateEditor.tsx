import React, { useState } from 'react';
import { X, Save, Eye, Palette, Type, Layout, Spacing } from 'lucide-react';
import { useTemplateStore } from '../../store/templateStore';
import { Template } from '../../types/resume';
import { v4 as uuidv4 } from 'uuid';

interface TemplateEditorProps {
  isOpen: boolean;
  onClose: () => void;
  templateId?: string;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({ isOpen, onClose, templateId }) => {
  const { templates, addTemplate, updateTemplate } = useTemplateStore();
  
  const existingTemplate = templateId ? templates.find(t => t.id === templateId) : null;
  
  const [template, setTemplate] = useState<Partial<Template>>(
    existingTemplate || {
      name: '',
      description: '',
      sections: ['personal', 'experience', 'education', 'skills'],
      styling: {
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        fontFamily: 'Inter, sans-serif',
        headerStyle: 'modern',
        layout: 'single-column',
        spacing: 'normal',
        borderRadius: '8px',
        shadowLevel: 'medium'
      },
      customCSS: '',
      isCustom: true
    }
  );

  const [activeTab, setActiveTab] = useState<'basic' | 'styling' | 'layout' | 'preview'>('basic');

  if (!isOpen) return null;

  const handleSave = () => {
    if (!template.name || !template.styling) return;

    const templateData: Template = {
      id: templateId || uuidv4(),
      name: template.name,
      description: template.description || '',
      thumbnail: '/templates/custom-thumb.png',
      sections: template.sections || [],
      styling: template.styling,
      customCSS: template.customCSS || '',
      isCustom: true
    };

    if (templateId) {
      updateTemplate(templateId, templateData);
    } else {
      addTemplate(templateData);
    }

    onClose();
  };

  const updateStyling = (key: string, value: string) => {
    setTemplate(prev => ({
      ...prev,
      styling: {
        ...prev.styling!,
        [key]: value
      }
    }));
  };

  const fontOptions = [
    'Inter, sans-serif',
    'Georgia, serif',
    'Helvetica, Arial, sans-serif',
    'Times New Roman, serif',
    'Poppins, sans-serif',
    'Roboto, sans-serif'
  ];

  const colorPresets = [
    '#2563eb', '#7c3aed', '#dc2626', '#059669', '#d97706', '#1f2937'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {templateId ? 'Edit Template' : 'Create Custom Template'}
            </h2>
            <p className="text-gray-600 mt-1">Design your own resume template</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          {[
            { id: 'basic', label: 'Basic Info', icon: Type },
            { id: 'styling', label: 'Styling', icon: Palette },
            { id: 'layout', label: 'Layout', icon: Layout },
            { id: 'preview', label: 'Preview', icon: Eye }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={template.name || ''}
                  onChange={(e) => setTemplate(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Template"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={template.description || ''}
                  onChange={(e) => setTemplate(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your template..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Default Sections
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {['personal', 'experience', 'education', 'skills', 'projects', 'publications'].map(section => (
                    <label key={section} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={template.sections?.includes(section) || false}
                        onChange={(e) => {
                          const sections = template.sections || [];
                          if (e.target.checked) {
                            setTemplate(prev => ({ ...prev, sections: [...sections, section] }));
                          } else {
                            setTemplate(prev => ({ ...prev, sections: sections.filter(s => s !== section) }));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700 capitalize">{section}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'styling' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Color
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={template.styling?.primaryColor || '#2563eb'}
                    onChange={(e) => updateStyling('primaryColor', e.target.value)}
                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <div className="flex space-x-2">
                    {colorPresets.map(color => (
                      <button
                        key={color}
                        onClick={() => updateStyling('primaryColor', color)}
                        className="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-400"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Secondary Color
                </label>
                <input
                  type="color"
                  value={template.styling?.secondaryColor || '#64748b'}
                  onChange={(e) => updateStyling('secondaryColor', e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Font Family
                </label>
                <select
                  value={template.styling?.fontFamily || 'Inter, sans-serif'}
                  onChange={(e) => updateStyling('fontFamily', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {fontOptions.map(font => (
                    <option key={font} value={font}>{font}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Border Radius
                </label>
                <select
                  value={template.styling?.borderRadius || '8px'}
                  onChange={(e) => updateStyling('borderRadius', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="0px">None</option>
                  <option value="4px">Small</option>
                  <option value="8px">Medium</option>
                  <option value="12px">Large</option>
                </select>
              </div>
            </div>
          )}

          {activeTab === 'layout' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Layout Type
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { value: 'single-column', label: 'Single Column', desc: 'Traditional single column layout' },
                    { value: 'two-column', label: 'Two Column', desc: 'Sidebar with main content area' }
                  ].map(layout => (
                    <label
                      key={layout.value}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        template.styling?.layout === layout.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="layout"
                        value={layout.value}
                        checked={template.styling?.layout === layout.value}
                        onChange={(e) => updateStyling('layout', e.target.value)}
                        className="sr-only"
                      />
                      <div className="font-medium text-gray-900">{layout.label}</div>
                      <div className="text-sm text-gray-600">{layout.desc}</div>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Spacing
                </label>
                <select
                  value={template.styling?.spacing || 'normal'}
                  onChange={(e) => updateStyling('spacing', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="compact">Compact</option>
                  <option value="normal">Normal</option>
                  <option value="relaxed">Relaxed</option>
                </select>
              </div>
            </div>
          )}

          {activeTab === 'preview' && (
            <div className="text-center py-8">
              <div className="text-gray-500">
                <Eye className="w-12 h-12 mx-auto mb-4" />
                <p>Template preview will be available soon</p>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!template.name}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{templateId ? 'Update' : 'Create'} Template</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TemplateEditor;

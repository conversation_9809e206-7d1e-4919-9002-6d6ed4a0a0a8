import React, { useState } from 'react';
import { 
  Edit3, 
  Save, 
  X, 
  Type, 
  List, 
  FileText,
  Plus,
  Trash2
} from 'lucide-react';
import { TemplateSection, SectionType } from '../../types/templateDesigner';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import RichContentEditor from './RichContentEditor';

interface SectionContentEditorProps {
  section: TemplateSection;
  isOpen: boolean;
  onClose: () => void;
}

interface ContentField {
  id: string;
  label: string;
  value: string;
  type: 'text' | 'textarea' | 'rich' | 'bullets';
  required?: boolean;
  placeholder?: string;
}

const SectionContentEditor: React.FC<SectionContentEditorProps> = ({
  section,
  isOpen,
  onClose
}) => {
  const { updateSection } = useTemplateDesignerStore();
  const [contentFields, setContentFields] = useState<ContentField[]>(() => 
    getDefaultFieldsForSection(section.type)
  );
  const [customFields, setCustomFields] = useState<ContentField[]>([]);

  if (!isOpen) return null;

  function getDefaultFieldsForSection(sectionType: SectionType): ContentField[] {
    switch (sectionType) {
      case 'personal':
        return [
          { id: 'fullName', label: 'Full Name', value: '', type: 'text', required: true },
          { id: 'email', label: 'Email', value: '', type: 'text', required: true },
          { id: 'phone', label: 'Phone', value: '', type: 'text' },
          { id: 'address', label: 'Address', value: '', type: 'textarea' },
          { id: 'summary', label: 'Professional Summary', value: '', type: 'rich' }
        ];
      case 'experience':
        return [
          { id: 'company', label: 'Company', value: '', type: 'text', required: true },
          { id: 'position', label: 'Position', value: '', type: 'text', required: true },
          { id: 'location', label: 'Location', value: '', type: 'text' },
          { id: 'startDate', label: 'Start Date', value: '', type: 'text' },
          { id: 'endDate', label: 'End Date', value: '', type: 'text' },
          { id: 'description', label: 'Responsibilities & Achievements', value: '', type: 'bullets' }
        ];
      case 'education':
        return [
          { id: 'institution', label: 'Institution', value: '', type: 'text', required: true },
          { id: 'degree', label: 'Degree', value: '', type: 'text', required: true },
          { id: 'field', label: 'Field of Study', value: '', type: 'text' },
          { id: 'graduationDate', label: 'Graduation Date', value: '', type: 'text' },
          { id: 'gpa', label: 'GPA', value: '', type: 'text' },
          { id: 'achievements', label: 'Achievements', value: '', type: 'bullets' }
        ];
      case 'skills':
        return [
          { id: 'skillsList', label: 'Skills', value: '', type: 'bullets', placeholder: 'Add your skills...' }
        ];
      case 'projects':
        return [
          { id: 'projectName', label: 'Project Name', value: '', type: 'text', required: true },
          { id: 'description', label: 'Description', value: '', type: 'rich' },
          { id: 'technologies', label: 'Technologies Used', value: '', type: 'bullets' },
          { id: 'link', label: 'Project Link', value: '', type: 'text' }
        ];
      case 'languages':
        return [
          { id: 'languagesList', label: 'Languages', value: '', type: 'bullets', placeholder: 'Language - Proficiency level' }
        ];
      case 'interests':
      case 'hobbies':
        return [
          { id: 'itemsList', label: section.type === 'interests' ? 'Interests' : 'Hobbies', value: '', type: 'bullets' }
        ];
      default:
        return [
          { id: 'content', label: 'Content', value: '', type: 'rich', placeholder: 'Enter your content...' }
        ];
    }
  }

  const handleFieldChange = (fieldId: string, value: string) => {
    setContentFields(fields => 
      fields.map(field => 
        field.id === fieldId ? { ...field, value } : field
      )
    );
  };

  const handleCustomFieldChange = (fieldId: string, value: string) => {
    setCustomFields(fields => 
      fields.map(field => 
        field.id === fieldId ? { ...field, value } : field
      )
    );
  };

  const addCustomField = () => {
    const fieldType = prompt('Field type (text, textarea, rich, bullets):') as any;
    const fieldLabel = prompt('Field label:');
    
    if (fieldType && fieldLabel) {
      const newField: ContentField = {
        id: `custom_${Date.now()}`,
        label: fieldLabel,
        value: '',
        type: fieldType,
        placeholder: `Enter ${fieldLabel.toLowerCase()}...`
      };
      setCustomFields([...customFields, newField]);
    }
  };

  const removeCustomField = (fieldId: string) => {
    setCustomFields(fields => fields.filter(field => field.id !== fieldId));
  };

  const handleSave = () => {
    // Convert fields to section content
    const allFields = [...contentFields, ...customFields];
    const contentData = allFields.reduce((acc, field) => {
      acc[field.id] = field.value;
      return acc;
    }, {} as Record<string, string>);

    updateSection(section.id, {
      content: {
        ...section.content,
        customContent: JSON.stringify(contentData)
      }
    });

    onClose();
  };

  const renderField = (field: ContentField, isCustom = false) => {
    const onChange = isCustom ? handleCustomFieldChange : handleFieldChange;

    return (
      <div key={field.id} className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-700">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </label>
          {isCustom && (
            <button
              onClick={() => removeCustomField(field.id)}
              className="p-1 text-red-400 hover:text-red-600 transition-colors"
              title="Remove field"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          )}
        </div>

        {field.type === 'text' && (
          <input
            type="text"
            value={field.value}
            onChange={(e) => onChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        )}

        {field.type === 'textarea' && (
          <textarea
            value={field.value}
            onChange={(e) => onChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            rows={3}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        )}

        {field.type === 'rich' && (
          <RichContentEditor
            content={field.value}
            onChange={(value) => onChange(field.id, value)}
            placeholder={field.placeholder}
            enableBullets={false}
          />
        )}

        {field.type === 'bullets' && (
          <RichContentEditor
            content={field.value}
            onChange={(value) => onChange(field.id, value)}
            placeholder={field.placeholder}
            enableBullets={true}
            bulletStyle={section.content.bullets?.style || 'disc'}
            customBulletSymbol={section.content.bullets?.customSymbol}
          />
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Edit3 className="w-5 h-5 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Edit Section Content</h3>
              <p className="text-sm text-gray-600">{section.title}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-6">
            {/* Default Fields */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-4">Section Fields</h4>
              <div className="space-y-4">
                {contentFields.map(field => renderField(field))}
              </div>
            </div>

            {/* Custom Fields */}
            {customFields.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4">Custom Fields</h4>
                <div className="space-y-4">
                  {customFields.map(field => renderField(field, true))}
                </div>
              </div>
            )}

            {/* Add Custom Field */}
            <div className="pt-4 border-t border-gray-200">
              <button
                onClick={addCustomField}
                className="flex items-center space-x-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Add Custom Field</span>
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>Save Content</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SectionContentEditor;

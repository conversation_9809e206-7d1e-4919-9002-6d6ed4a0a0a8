import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, ExternalLink } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { useResumeStore } from '../../store/resumeStore';
import { Publication } from '../../types/resume';
import GrammarHighlighter from '../GrammarChecker/GrammarHighlighter';

const PublicationsForm: React.FC = () => {
  const { currentResume, updateSection } = useResumeStore();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  
  const publicationsSection = currentResume?.sections.find(s => s.type === 'publications');
  const publications = (publicationsSection?.data || []) as Publication[];

  const { register, handleSubmit, reset, setValue, watch } = useForm<Publication>();
  const watchedDescription = watch('description');

  const handleSave = (data: Publication) => {
    const newPublications = editingId
      ? publications.map(pub => pub.id === editingId ? { ...data, id: editingId } : pub)
      : [...publications, { ...data, id: uuidv4() }];
    
    if (publicationsSection) {
      updateSection(publicationsSection.id, newPublications);
    }
    
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleEdit = (publication: Publication) => {
    reset(publication);
    setEditingId(publication.id);
    setIsAdding(false);
  };

  const handleDelete = (id: string) => {
    const newPublications = publications.filter(pub => pub.id !== id);
    if (publicationsSection) {
      updateSection(publicationsSection.id, newPublications);
    }
  };

  const handleCancel = () => {
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleDescriptionChange = (text: string) => {
    setValue('description', text);
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Publications</h3>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add</span>
        </button>
      </div>

      {(isAdding || editingId) && (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <form onSubmit={handleSubmit(handleSave)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title *
              </label>
              <input
                {...register('title', { required: true })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Authors *
              </label>
              <input
                {...register('authors', { required: true })}
                placeholder="Smith, J., Doe, A., Johnson, B."
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Journal/Conference *
                </label>
                <input
                  {...register('journal', { required: true })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Year *
                </label>
                <input
                  {...register('year', { required: true })}
                  type="number"
                  min="1900"
                  max="2030"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                URL/DOI
              </label>
              <input
                {...register('url')}
                type="url"
                placeholder="https://doi.org/10.1000/xyz123 or publication URL"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <GrammarHighlighter
                text={watchedDescription || ''}
                onChange={handleDescriptionChange}
                placeholder="Brief description of the publication..."
                className="min-h-20"
              />
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="flex items-center space-x-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-3">
        {publications.map((publication) => (
          <div key={publication.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-gray-900">{publication.title}</h4>
                  {publication.url && (
                    <a
                      href={publication.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  )}
                </div>
                <p className="text-sm text-gray-600 mt-1">{publication.authors}</p>
                <p className="text-sm text-gray-600">
                  <em>{publication.journal}</em> ({publication.year})
                </p>
                {publication.description && (
                  <p className="text-sm text-gray-700 mt-2">{publication.description}</p>
                )}
              </div>
              <div className="flex space-x-1 ml-4">
                <button
                  onClick={() => handleEdit(publication)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(publication.id)}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PublicationsForm;
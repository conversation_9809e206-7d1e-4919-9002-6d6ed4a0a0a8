import React, { useState } from 'react';
import { 
  Layout, 
  Eye, 
  Save, 
  Settings, 
  Plus, 
  X, 
  Grid,
  Sidebar,
  Columns,
  Square
} from 'lucide-react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent } from '@dnd-kit/core';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import SectionPalette from './SectionPalette';
import LayoutCanvas from './LayoutCanvas';
import SectionPropertiesPanel from './SectionPropertiesPanel';
import LayoutPropertiesPanel from './LayoutPropertiesPanel';

interface TemplateDesignerProps {
  isOpen: boolean;
  onClose: () => void;
  layoutId?: string;
}

const TemplateDesigner: React.FC<TemplateDesignerProps> = ({ isOpen, onClose, layoutId }) => {
  const {
    currentLayout,
    isDesignMode,
    previewMode,
    selectedSection,
    draggedSection,
    setDesignMode,
    setPreviewMode,
    loadLayout,
    createNewLayout,
    saveLayout,
    startDrag,
    endDrag,
    handleDrop,
    setDropZone
  } = useTemplateDesignerStore();

  const [activePanel, setActivePanel] = useState<'sections' | 'properties' | 'layout'>('sections');
  const [layoutName, setLayoutName] = useState('');
  const [layoutDescription, setLayoutDescription] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  React.useEffect(() => {
    if (isOpen) {
      if (layoutId) {
        loadLayout(layoutId);
      } else if (!currentLayout) {
        setShowCreateDialog(true);
      }
      setDesignMode(true);
    } else {
      setDesignMode(false);
    }
  }, [isOpen, layoutId]);

  if (!isOpen) return null;

  const handleCreateLayout = () => {
    if (layoutName.trim()) {
      createNewLayout(layoutName.trim(), layoutDescription.trim());
      setShowCreateDialog(false);
      setLayoutName('');
      setLayoutDescription('');
    }
  };

  const handleSave = () => {
    saveLayout();
    // Show success message
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const sectionData = active.data.current;
    if (sectionData?.section && sectionData?.sourceZoneId) {
      startDrag(sectionData.section, sectionData.sourceZoneId);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    if (over) {
      const dropData = over.data.current;
      if (dropData?.zoneId !== undefined && dropData?.position !== undefined) {
        setDropZone({
          zoneId: dropData.zoneId,
          position: dropData.position,
          isValid: dropData.isValid || false
        });
      }
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    handleDrop();
    endDrag();
  };

  if (showCreateDialog) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Template</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name *
                </label>
                <input
                  type="text"
                  value={layoutName}
                  onChange={(e) => setLayoutName(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Template"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={layoutDescription}
                  onChange={(e) => setLayoutDescription(e.target.value)}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your template..."
                />
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateLayout}
                disabled={!layoutName.trim()}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors"
              >
                Create Template
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-100 z-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Layout className="w-6 h-6 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">Template Designer</h1>
            </div>
            {currentLayout && (
              <div className="text-sm text-gray-600">
                Editing: <span className="font-medium">{currentLayout.name}</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode(false)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  !previewMode
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Design
              </button>
              <button
                onClick={() => setPreviewMode(true)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  previewMode
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Preview
              </button>
            </div>

            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </button>

            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar */}
        {!previewMode && (
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            {/* Panel Tabs */}
            <div className="flex border-b border-gray-200">
              {[
                { id: 'sections', label: 'Sections', icon: Grid },
                { id: 'properties', label: 'Properties', icon: Settings },
                { id: 'layout', label: 'Layout', icon: Columns }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActivePanel(tab.id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 text-sm font-medium transition-colors ${
                    activePanel === tab.id
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Panel Content */}
            <div className="flex-1 overflow-y-auto">
              {activePanel === 'sections' && <SectionPalette />}
              {activePanel === 'properties' && <SectionPropertiesPanel />}
              {activePanel === 'layout' && <LayoutPropertiesPanel />}
            </div>
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 overflow-auto bg-gray-50">
          <DndContext
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
          >
            <LayoutCanvas />
          </DndContext>
        </div>
      </div>
    </div>
  );
};

export default TemplateDesigner;

import React from 'react';
import { 
  Plus, 
  Trash2, 
  Settings, 
  Palette, 
  Type,
  Layout,
  Sidebar,
  Square,
  Columns
} from 'lucide-react';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import { LayoutZone } from '../../types/templateDesigner';

const LayoutPropertiesPanel: React.FC = () => {
  const { 
    currentLayout, 
    updateLayoutInfo, 
    addZone, 
    updateZone, 
    removeZone 
  } = useTemplateDesignerStore();

  if (!currentLayout) {
    return (
      <div className="p-4 text-center text-gray-500">
        <div className="text-sm">No layout loaded</div>
      </div>
    );
  }

  const updateGlobalStyling = (key: string, value: string) => {
    updateLayoutInfo({
      globalStyling: {
        ...currentLayout.globalStyling,
        [key]: value
      }
    });
  };

  const handleAddZone = () => {
    const zoneType = prompt('Zone type (left-sidebar, right-sidebar, main-content, header, footer, full-width):');
    const zoneName = prompt('Zone name:');
    
    if (zoneType && zoneName) {
      addZone({
        name: zoneName,
        type: zoneType as any,
        width: zoneType.includes('sidebar') ? '30%' : '100%',
        sections: []
      });
    }
  };

  const zoneTypeIcons = {
    'header': Square,
    'footer': Square,
    'left-sidebar': Sidebar,
    'right-sidebar': Sidebar,
    'main-content': Layout,
    'full-width': Columns
  };

  return (
    <div className="p-4 space-y-6">
      <div>
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Layout Properties</h3>
        <div className="text-xs text-gray-600 mb-4">
          Configure global layout settings
        </div>
      </div>

      {/* Layout Information */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
          Layout Information
        </h4>
        
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Layout Name
          </label>
          <input
            type="text"
            value={currentLayout.name}
            onChange={(e) => updateLayoutInfo({ name: e.target.value })}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={currentLayout.description}
            onChange={(e) => updateLayoutInfo({ description: e.target.value })}
            rows={2}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Global Styling */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
          <Palette className="w-3 h-3 mr-1" />
          Global Styling
        </h4>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Font Family
          </label>
          <select
            value={currentLayout.globalStyling.fontFamily}
            onChange={(e) => updateGlobalStyling('fontFamily', e.target.value)}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="Inter, sans-serif">Inter</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="Helvetica, Arial, sans-serif">Helvetica</option>
            <option value="Times New Roman, serif">Times New Roman</option>
            <option value="Poppins, sans-serif">Poppins</option>
            <option value="Roboto, sans-serif">Roboto</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Primary Color
            </label>
            <input
              type="color"
              value={currentLayout.globalStyling.primaryColor}
              onChange={(e) => updateGlobalStyling('primaryColor', e.target.value)}
              className="w-full h-8 border border-gray-300 rounded cursor-pointer"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Secondary Color
            </label>
            <input
              type="color"
              value={currentLayout.globalStyling.secondaryColor}
              onChange={(e) => updateGlobalStyling('secondaryColor', e.target.value)}
              className="w-full h-8 border border-gray-300 rounded cursor-pointer"
            />
          </div>
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Background Color
          </label>
          <input
            type="color"
            value={currentLayout.globalStyling.backgroundColor}
            onChange={(e) => updateGlobalStyling('backgroundColor', e.target.value)}
            className="w-full h-8 border border-gray-300 rounded cursor-pointer"
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Page Margin
          </label>
          <select
            value={currentLayout.globalStyling.pageMargin}
            onChange={(e) => updateGlobalStyling('pageMargin', e.target.value)}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1rem">Small (1rem)</option>
            <option value="2rem">Normal (2rem)</option>
            <option value="3rem">Large (3rem)</option>
            <option value="4rem">Extra Large (4rem)</option>
          </select>
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Section Spacing
          </label>
          <select
            value={currentLayout.globalStyling.sectionSpacing}
            onChange={(e) => updateGlobalStyling('sectionSpacing', e.target.value)}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="0.5rem">Tight (0.5rem)</option>
            <option value="1rem">Compact (1rem)</option>
            <option value="1.5rem">Normal (1.5rem)</option>
            <option value="2rem">Relaxed (2rem)</option>
          </select>
        </div>
      </div>

      {/* Layout Zones */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
            <Layout className="w-3 h-3 mr-1" />
            Layout Zones
          </h4>
          <button
            onClick={handleAddZone}
            className="p-1 text-blue-600 hover:text-blue-700 transition-colors"
            title="Add Zone"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-2">
          {currentLayout.zones.map((zone) => {
            const IconComponent = zoneTypeIcons[zone.type] || Layout;
            
            return (
              <div key={zone.id} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-900">
                      {zone.name}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {zone.type}
                    </span>
                  </div>
                  <button
                    onClick={() => removeZone(zone.id)}
                    className="p-1 text-red-400 hover:text-red-600 transition-colors"
                    title="Remove Zone"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </div>

                <div className="space-y-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Zone Name
                    </label>
                    <input
                      type="text"
                      value={zone.name}
                      onChange={(e) => updateZone(zone.id, { name: e.target.value })}
                      className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>

                  {(zone.type === 'left-sidebar' || zone.type === 'right-sidebar') && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Width
                      </label>
                      <select
                        value={zone.width}
                        onChange={(e) => updateZone(zone.id, { width: e.target.value })}
                        className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="25%">25%</option>
                        <option value="30%">30%</option>
                        <option value="35%">35%</option>
                        <option value="40%">40%</option>
                      </select>
                    </div>
                  )}

                  {zone.maxSections && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Max Sections
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={zone.maxSections}
                        onChange={(e) => updateZone(zone.id, { maxSections: parseInt(e.target.value) })}
                        className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    {zone.sections.length} section{zone.sections.length !== 1 ? 's' : ''}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Layout Templates */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
          Quick Templates
        </h4>
        
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => {
              // Add predefined single column layout
              console.log('Apply single column layout');
            }}
            className="p-2 border border-gray-300 rounded text-xs hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            Single Column
          </button>
          
          <button
            onClick={() => {
              // Add predefined two column layout
              console.log('Apply two column layout');
            }}
            className="p-2 border border-gray-300 rounded text-xs hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            Two Column
          </button>
        </div>
      </div>
    </div>
  );
};

export default LayoutPropertiesPanel;

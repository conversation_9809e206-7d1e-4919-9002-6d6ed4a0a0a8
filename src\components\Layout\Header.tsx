import React, { useState } from 'react';
import { FileText, Undo, Redo, Download, Save, Palette } from 'lucide-react';
import { useResumeStore } from '../../store/resumeStore';
import TemplateSelector from '../Templates/TemplateSelector';
import toast from 'react-hot-toast';

interface HeaderProps {
  onExportPDF: () => void;
  onExportWord: () => void;
}

const Header: React.FC<HeaderProps> = ({ onExportPDF, onExportWord }) => {
  const { currentResume, undo, redo, canUndo, canRedo } = useResumeStore();
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  const handleUndo = () => {
    if (canUndo()) {
      undo();
      toast.success('Action undone');
    }
  };

  const handleRedo = () => {
    if (canRedo()) {
      redo();
      toast.success('Action redone');
    }
  };

  const handleSave = () => {
    // In a real app, this would save to a backend
    localStorage.setItem('resume', JSON.stringify(currentResume));
    toast.success('Resume saved locally');
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <FileText className="w-8 h-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Resume Builder</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleUndo}
              disabled={!canUndo()}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Undo"
            >
              <Undo className="w-5 h-5" />
            </button>
            <button
              onClick={handleRedo}
              disabled={!canRedo()}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Redo"
            >
              <Redo className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowTemplateSelector(true)}
            className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <Palette className="w-4 h-4" />
            <span>Templates</span>
          </button>

          <button
            onClick={handleSave}
            className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>

          <div className="flex items-center space-x-2">
            <button
              onClick={onExportPDF}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>PDF</span>
            </button>
            <button
              onClick={onExportWord}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white hover:bg-green-700 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Word</span>
            </button>
          </div>
        </div>
      </div>

      <TemplateSelector
        isOpen={showTemplateSelector}
        onClose={() => setShowTemplateSelector(false)}
      />
    </header>
  );
};

export default Header;
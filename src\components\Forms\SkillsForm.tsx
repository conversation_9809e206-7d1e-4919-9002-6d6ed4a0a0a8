import React, { useState } from 'react';
import { Plus, X, Trash2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { useResumeStore } from '../../store/resumeStore';
import { Skill } from '../../types/resume';

const SkillsForm: React.FC = () => {
  const { currentResume, updateSection } = useResumeStore();
  const [isAdding, setIsAdding] = useState(false);
  
  const skillsSection = currentResume?.sections.find(s => s.type === 'skills');
  const skills = (skillsSection?.data || []) as Skill[];

  const { register, handleSubmit, reset } = useForm<Skill>();

  const handleSave = (data: Skill) => {
    const newSkills = [...skills, { ...data, id: uuidv4() }];
    
    if (skillsSection) {
      updateSection(skillsSection.id, newSkills);
    }
    
    reset();
    setIsAdding(false);
  };

  const handleDelete = (id: string) => {
    const newSkills = skills.filter(skill => skill.id !== id);
    if (skillsSection) {
      updateSection(skillsSection.id, newSkills);
    }
  };

  const handleCancel = () => {
    reset();
    setIsAdding(false);
  };

  const getSkillsByCategory = () => {
    const categorized: { [key: string]: Skill[] } = {};
    skills.forEach(skill => {
      if (!categorized[skill.category]) {
        categorized[skill.category] = [];
      }
      categorized[skill.category].push(skill);
    });
    return categorized;
  };

  const skillsByCategory = getSkillsByCategory();

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Skills</h3>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add</span>
        </button>
      </div>

      {isAdding && (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <form onSubmit={handleSubmit(handleSave)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Skill Name *
              </label>
              <input
                {...register('name', { required: true })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="JavaScript"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category *
              </label>
              <input
                {...register('category', { required: true })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Programming Languages"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Proficiency Level *
              </label>
              <select
                {...register('level', { required: true })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select level</option>
                <option value="Beginner">Beginner</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Advanced">Advanced</option>
                <option value="Expert">Expert</option>
              </select>
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Add Skill
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="flex items-center space-x-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-6">
        {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
          <div key={category}>
            <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
            <div className="grid grid-cols-1 gap-2">
              {categorySkills.map((skill) => (
                <div key={skill.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-white">
                  <div className="flex-1">
                    <span className="text-sm font-medium text-gray-900">{skill.name}</span>
                    <div className="flex items-center mt-1">
                      <div className="w-24 h-2 bg-gray-200 rounded-full mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            skill.level === 'Expert' ? 'bg-green-500 w-full' :
                            skill.level === 'Advanced' ? 'bg-blue-500 w-3/4' :
                            skill.level === 'Intermediate' ? 'bg-yellow-500 w-1/2' :
                            'bg-red-500 w-1/4'
                          }`}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">{skill.level}</span>
                    </div>
                  </div>
                  <button
                    onClick={() => handleDelete(skill.id)}
                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillsForm;
export interface LayoutZone {
  id: string;
  name: string;
  type: 'left-sidebar' | 'right-sidebar' | 'main-content' | 'header' | 'footer' | 'full-width';
  width: string; // CSS width value
  sections: TemplateSection[];
  allowedSectionTypes?: string[];
  maxSections?: number;
}

export interface TemplateSection {
  id: string;
  type: SectionType;
  title: string;
  customTitle?: string;
  visible: boolean;
  order: number;
  zoneId: string;
  styling: SectionStyling;
  content: SectionContent;
}

export interface SectionStyling {
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  borderWidth?: string;
  borderRadius?: string;
  padding?: string;
  margin?: string;
  fontSize?: string;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right';
  showBorder?: boolean;
  showBackground?: boolean;
}

export interface SectionContent {
  heading?: HeadingConfig;
  bullets?: BulletConfig;
  customContent?: string;
  showDivider?: boolean;
  dividerStyle?: 'line' | 'dots' | 'dashes';
}

export interface HeadingConfig {
  text: string;
  style: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  color?: string;
  alignment?: 'left' | 'center' | 'right';
  showUnderline?: boolean;
  showIcon?: boolean;
  iconName?: string;
}

export interface BulletConfig {
  enabled: boolean;
  style: 'disc' | 'circle' | 'square' | 'decimal' | 'none' | 'custom';
  customSymbol?: string;
  indentation?: string;
  spacing?: string;
}

export type SectionType = 
  | 'personal' 
  | 'experience' 
  | 'education' 
  | 'skills' 
  | 'projects' 
  | 'publications'
  | 'certifications'
  | 'awards'
  | 'languages'
  | 'volunteer'
  | 'interests'
  | 'hobbies'
  | 'references'
  | 'custom';

export interface TemplateLayout {
  id: string;
  name: string;
  description: string;
  zones: LayoutZone[];
  globalStyling: {
    fontFamily: string;
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    pageMargin: string;
    sectionSpacing: string;
  };
  isCustom: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DraggedSection {
  section: TemplateSection;
  sourceZoneId: string;
}

export interface DropZoneInfo {
  zoneId: string;
  position: number;
  isValid: boolean;
}

// Predefined section configurations
export const SECTION_CONFIGS: Record<SectionType, {
  defaultTitle: string;
  description: string;
  icon: string;
  defaultZone: string;
  allowedZones: string[];
  hasContent: boolean;
  contentType: 'list' | 'text' | 'structured' | 'custom';
}> = {
  personal: {
    defaultTitle: 'Personal Information',
    description: 'Contact details and basic info',
    icon: 'User',
    defaultZone: 'header',
    allowedZones: ['header', 'left-sidebar', 'main-content'],
    hasContent: true,
    contentType: 'structured'
  },
  experience: {
    defaultTitle: 'Work Experience',
    description: 'Professional work history',
    icon: 'Briefcase',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'full-width'],
    hasContent: true,
    contentType: 'structured'
  },
  education: {
    defaultTitle: 'Education',
    description: 'Academic background',
    icon: 'GraduationCap',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'right-sidebar', 'full-width'],
    hasContent: true,
    contentType: 'structured'
  },
  skills: {
    defaultTitle: 'Skills',
    description: 'Technical and soft skills',
    icon: 'Zap',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar', 'main-content'],
    hasContent: true,
    contentType: 'list'
  },
  projects: {
    defaultTitle: 'Projects',
    description: 'Personal and professional projects',
    icon: 'Folder',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'full-width'],
    hasContent: true,
    contentType: 'structured'
  },
  publications: {
    defaultTitle: 'Publications',
    description: 'Research papers and articles',
    icon: 'BookOpen',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'full-width'],
    hasContent: true,
    contentType: 'list'
  },
  certifications: {
    defaultTitle: 'Certifications',
    description: 'Professional certifications',
    icon: 'Award',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar', 'main-content'],
    hasContent: true,
    contentType: 'list'
  },
  awards: {
    defaultTitle: 'Awards & Honors',
    description: 'Recognition and achievements',
    icon: 'Trophy',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar', 'main-content'],
    hasContent: true,
    contentType: 'list'
  },
  languages: {
    defaultTitle: 'Languages',
    description: 'Language proficiency',
    icon: 'Globe',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar'],
    hasContent: true,
    contentType: 'list'
  },
  volunteer: {
    defaultTitle: 'Volunteer Experience',
    description: 'Community service',
    icon: 'Heart',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'full-width'],
    hasContent: true,
    contentType: 'structured'
  },
  interests: {
    defaultTitle: 'Interests',
    description: 'Professional interests',
    icon: 'Star',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar', 'footer'],
    hasContent: true,
    contentType: 'list'
  },
  hobbies: {
    defaultTitle: 'Hobbies',
    description: 'Personal hobbies and activities',
    icon: 'Gamepad2',
    defaultZone: 'right-sidebar',
    allowedZones: ['right-sidebar', 'left-sidebar', 'footer'],
    hasContent: true,
    contentType: 'list'
  },
  references: {
    defaultTitle: 'References',
    description: 'Professional references',
    icon: 'Users',
    defaultZone: 'footer',
    allowedZones: ['footer', 'main-content', 'full-width'],
    hasContent: true,
    contentType: 'structured'
  },
  custom: {
    defaultTitle: 'Custom Section',
    description: 'Create your own section',
    icon: 'Plus',
    defaultZone: 'main-content',
    allowedZones: ['main-content', 'left-sidebar', 'right-sidebar', 'full-width', 'header', 'footer'],
    hasContent: true,
    contentType: 'custom'
  }
};

// Default layout templates
export const DEFAULT_LAYOUTS: TemplateLayout[] = [
  {
    id: 'classic-two-column',
    name: 'Classic Two Column',
    description: 'Traditional layout with sidebar and main content',
    zones: [
      {
        id: 'header',
        name: 'Header',
        type: 'header',
        width: '100%',
        sections: [],
        allowedSectionTypes: ['personal'],
        maxSections: 1
      },
      {
        id: 'left-sidebar',
        name: 'Left Sidebar',
        type: 'left-sidebar',
        width: '30%',
        sections: [],
        allowedSectionTypes: ['skills', 'languages', 'certifications', 'interests', 'hobbies']
      },
      {
        id: 'main-content',
        name: 'Main Content',
        type: 'main-content',
        width: '70%',
        sections: [],
        allowedSectionTypes: ['experience', 'education', 'projects', 'volunteer', 'publications']
      }
    ],
    globalStyling: {
      fontFamily: 'Inter, sans-serif',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      backgroundColor: '#ffffff',
      pageMargin: '2rem',
      sectionSpacing: '1.5rem'
    },
    isCustom: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

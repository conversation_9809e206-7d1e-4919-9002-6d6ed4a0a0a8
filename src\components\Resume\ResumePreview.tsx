import React from 'react';
import { useResumeStore } from '../../store/resumeStore';
import { Mail, Phone, MapPin, Globe, Linkedin } from 'lucide-react';

const ResumePreview: React.FC = () => {
  const { currentResume } = useResumeStore();

  if (!currentResume) {
    return (
      <div className="w-full h-full flex items-center justify-center text-gray-500">
        No resume data available
      </div>
    );
  }

  const { personal, sections } = currentResume;
  const visibleSections = sections
    .filter(section => section.visible)
    .sort((a, b) => a.order - b.order);

  return (
    <div id="resume-preview" className="bg-white p-8 shadow-lg" style={{ width: '794px', minHeight: '1123px' }}>
      {/* Header */}
      <header className="mb-8 pb-6 border-b-2 border-gray-200">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">
          {personal.fullName || 'Your Name'}
        </h1>
        
        <div className="flex flex-wrap gap-4 text-sm text-gray-600">
          {personal.email && (
            <div className="flex items-center space-x-1">
              <Mail className="w-4 h-4" />
              <span>{personal.email}</span>
            </div>
          )}
          {personal.phone && (
            <div className="flex items-center space-x-1">
              <Phone className="w-4 h-4" />
              <span>{personal.phone}</span>
            </div>
          )}
          {personal.address && (
            <div className="flex items-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>{personal.address}</span>
            </div>
          )}
          {personal.linkedin && (
            <div className="flex items-center space-x-1">
              <Linkedin className="w-4 h-4" />
              <span>{personal.linkedin}</span>
            </div>
          )}
          {personal.website && (
            <div className="flex items-center space-x-1">
              <Globe className="w-4 h-4" />
              <span>{personal.website}</span>
            </div>
          )}
        </div>
      </header>

      {/* Professional Summary */}
      {personal.summary && (
        <section className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-3 pb-1 border-b border-gray-300">
            Professional Summary
          </h2>
          <p className="text-gray-700 leading-relaxed">{personal.summary}</p>
        </section>
      )}

      {/* Dynamic Sections */}
      {visibleSections.map((section) => (
        <section key={section.id} className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-3 pb-1 border-b border-gray-300">
            {section.title}
          </h2>
          
          {section.type === 'experience' && (
            <div className="space-y-4">
              {(section.data || []).map((exp: any, index: number) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="font-semibold text-gray-900">{exp.position}</h3>
                      <p className="text-gray-700">{exp.company}</p>
                    </div>
                    <div className="text-right text-sm text-gray-600">
                      <p>{exp.startDate} - {exp.current ? 'Present' : exp.endDate}</p>
                      {exp.location && <p>{exp.location}</p>}
                    </div>
                  </div>
                  {exp.description && (
                    <p className="text-gray-700 text-sm mt-2">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}

          {section.type === 'education' && (
            <div className="space-y-4">
              {(section.data || []).map((edu: any, index: number) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="font-semibold text-gray-900">{edu.degree} in {edu.field}</h3>
                      <p className="text-gray-700">{edu.institution}</p>
                    </div>
                    <div className="text-right text-sm text-gray-600">
                      <p>{edu.startDate} - {edu.current ? 'Present' : edu.endDate}</p>
                      {edu.location && <p>{edu.location}</p>}
                      {edu.gpa && <p>GPA: {edu.gpa}</p>}
                    </div>
                  </div>
                  {edu.description && (
                    <p className="text-gray-700 text-sm mt-2">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}

          {section.type === 'skills' && (
            <div className="space-y-3">
              {(() => {
                const skillsByCategory: { [key: string]: any[] } = {};
                (section.data || []).forEach((skill: any) => {
                  if (!skillsByCategory[skill.category]) {
                    skillsByCategory[skill.category] = [];
                  }
                  skillsByCategory[skill.category].push(skill);
                });
                
                return Object.entries(skillsByCategory).map(([category, categorySkills]) => (
                  <div key={category}>
                    <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                    <div className="flex flex-wrap gap-2">
                      {categorySkills.map((skill, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full"
                        >
                          {skill.name} ({skill.level})
                        </span>
                      ))}
                    </div>
                  </div>
                ));
              })()}
            </div>
          )}

          {section.type === 'projects' && (
            <div className="space-y-4">
              {(section.data || []).map((project: any, index: number) => (
                <div key={index}>
                  <div className="flex justify-between items-start mb-1">
                    <h3 className="font-semibold text-gray-900">{project.name}</h3>
                    <p className="text-sm text-gray-600">
                      {project.startDate}{project.endDate ? ` - ${project.endDate}` : ' - Ongoing'}
                    </p>
                  </div>
                  {project.description && (
                    <p className="text-gray-700 text-sm mb-2">{project.description}</p>
                  )}
                  {project.technologies && project.technologies.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech: string, techIndex: number) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {section.type === 'publications' && (
            <div className="space-y-4">
              {(section.data || []).map((pub: any, index: number) => (
                <div key={index}>
                  <h3 className="font-semibold text-gray-900 mb-1">{pub.title}</h3>
                  <p className="text-gray-700 text-sm">{pub.authors}</p>
                  <p className="text-gray-700 text-sm">
                    <em>{pub.journal}</em> ({pub.year})
                  </p>
                  {pub.description && (
                    <p className="text-gray-700 text-sm mt-2">{pub.description}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </section>
      ))}
    </div>
  );
};

export default ResumePreview;
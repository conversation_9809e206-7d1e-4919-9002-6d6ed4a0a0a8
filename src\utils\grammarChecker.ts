export interface GrammarError {
  id: string;
  text: string;
  offset: number;
  length: number;
  message: string;
  suggestions: string[];
  type: 'grammar' | 'spelling' | 'style';
}

class GrammarChecker {
  private static instance: GrammarChecker;
  private commonMisspellings: Map<string, string[]>;
  private grammarRules: RegExp[];

  private constructor() {
    this.commonMisspellings = new Map([
      ['teh', ['the']],
      ['recieve', ['receive']],
      ['occurence', ['occurrence']],
      ['seperate', ['separate']],
      ['definately', ['definitely']],
      ['managment', ['management']],
      ['developement', ['development']],
      ['experiance', ['experience']],
      ['responsibile', ['responsible']],
      ['acheivement', ['achievement']]
    ]);

    this.grammarRules = [
      /\bi\s+(?!am|was|were|will|would|could|should|might|can|do|did|have|had)\w+/gi,
      /\b(a)\s+([aeiou]\w+)/gi,
      /\b(an)\s+([bcdfghjklmnpqrstvwxyz]\w+)/gi,
    ];
  }

  public static getInstance(): GrammarChecker {
    if (!GrammarChecker.instance) {
      GrammarChecker.instance = new GrammarChecker();
    }
    return GrammarChecker.instance;
  }

  public checkText(text: string): GrammarError[] {
    const errors: GrammarError[] = [];
    
    // Check spelling
    const spellingErrors = this.checkSpelling(text);
    errors.push(...spellingErrors);

    // Check basic grammar
    const grammarErrors = this.checkGrammar(text);
    errors.push(...grammarErrors);

    return errors;
  }

  private checkSpelling(text: string): GrammarError[] {
    const errors: GrammarError[] = [];
    const words = text.split(/\s+/);
    let offset = 0;

    words.forEach((word) => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      const suggestions = this.commonMisspellings.get(cleanWord);
      
      if (suggestions) {
        errors.push({
          id: `spell_${Date.now()}_${Math.random()}`,
          text: word,
          offset,
          length: word.length,
          message: `"${word}" may be misspelled`,
          suggestions,
          type: 'spelling'
        });
      }
      
      offset += word.length + 1; // +1 for space
    });

    return errors;
  }

  private checkGrammar(text: string): GrammarError[] {
    const errors: GrammarError[] = [];

    // Check for 'I' capitalization
    const iMatches = text.matchAll(/\bi\s/gi);
    for (const match of iMatches) {
      if (match[0] === 'i ') {
        errors.push({
          id: `grammar_${Date.now()}_${Math.random()}`,
          text: match[0],
          offset: match.index!,
          length: 1,
          message: '"I" should be capitalized',
          suggestions: ['I'],
          type: 'grammar'
        });
      }
    }

    // Check a/an usage
    const aMatches = text.matchAll(/\ba\s+([aeiou]\w+)/gi);
    for (const match of aMatches) {
      errors.push({
        id: `grammar_${Date.now()}_${Math.random()}`,
        text: match[0],
        offset: match.index!,
        length: match[0].length,
        message: 'Use "an" before words starting with vowels',
        suggestions: [`an ${match[1]}`],
        type: 'grammar'
      });
    }

    const anMatches = text.matchAll(/\ban\s+([bcdfghjklmnpqrstvwxyz]\w+)/gi);
    for (const match of anMatches) {
      errors.push({
        id: `grammar_${Date.now()}_${Math.random()}`,
        text: match[0],
        offset: match.index!,
        length: match[0].length,
        message: 'Use "a" before words starting with consonants',
        suggestions: [`a ${match[1]}`],
        type: 'grammar'
      });
    }

    return errors;
  }
}

export const grammarChecker = GrammarChecker.getInstance();
import React, { useState } from 'react';
import { Plus, <PERSON>ting<PERSON>, Eye, EyeOff, GripVertical } from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useResumeStore } from '../../store/resumeStore';
import PersonalInfoForm from '../Forms/PersonalInfoForm';
import ExperienceForm from '../Forms/ExperienceForm';
import EducationForm from '../Forms/EducationForm';
import SkillsForm from '../Forms/SkillsForm';
import ProjectsForm from '../Forms/ProjectsForm';
import PublicationsForm from '../Forms/PublicationsForm';
import AddSectionModal from './AddSectionModal';

interface SortableSectionItemProps {
  section: any;
  activeSection: string;
  onSectionClick: (type: string) => void;
  onToggleVisibility: (id: string) => void;
}

const SortableSectionItem: React.FC<SortableSectionItemProps> = ({
  section,
  activeSection,
  onSectionClick,
  onToggleVisibility,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: section.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center ${isDragging ? 'z-50' : ''}`}
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600 transition-colors"
      >
        <GripVertical className="w-4 h-4" />
      </div>
      <button
        onClick={() => onSectionClick(section.type)}
        className={`flex-1 text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
          activeSection === section.type
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        {section.title}
      </button>
      <button
        onClick={() => onToggleVisibility(section.id)}
        className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
        title={section.visible ? 'Hide section' : 'Show section'}
      >
        {section.visible ? (
          <Eye className="w-4 h-4" />
        ) : (
          <EyeOff className="w-4 h-4" />
        )}
      </button>
    </div>
  );
};

const Sidebar: React.FC = () => {
  const { currentResume, toggleSectionVisibility, addSection, reorderSections } = useResumeStore();
  const [activeSection, setActiveSection] = useState<string>('personal');
  const [showAddModal, setShowAddModal] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  if (!currentResume) return null;

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = currentResume.sections.findIndex(section => section.id === active.id);
      const newIndex = currentResume.sections.findIndex(section => section.id === over.id);

      const newSections = arrayMove(currentResume.sections, oldIndex, newIndex);
      reorderSections(newSections);
    }
  };

  const renderForm = () => {
    switch (activeSection) {
      case 'personal':
        return <PersonalInfoForm />;
      case 'experience':
        return <ExperienceForm />;
      case 'education':
        return <EducationForm />;
      case 'skills':
        return <SkillsForm />;
      case 'projects':
        return <ProjectsForm />;
      case 'publications':
        return <PublicationsForm />;
      default:
        return <div className="p-4 text-gray-500">Select a section to edit</div>;
    }
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Edit Resume</h2>

        <nav className="space-y-1">
          <button
            onClick={() => setActiveSection('personal')}
            className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activeSection === 'personal'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            Personal Information
          </button>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={currentResume.sections.map(s => s.id)}
              strategy={verticalListSortingStrategy}
            >
              {currentResume.sections.map((section) => (
                <SortableSectionItem
                  key={section.id}
                  section={section}
                  activeSection={activeSection}
                  onSectionClick={setActiveSection}
                  onToggleVisibility={toggleSectionVisibility}
                />
              ))}
            </SortableContext>
          </DndContext>
        </nav>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add Section</span>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto min-h-0">
        {renderForm()}
      </div>

      <AddSectionModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAdd={(type, title) => {
          addSection(type as any, title);
          setShowAddModal(false);
        }}
      />
    </div>
  );
};

export default Sidebar;
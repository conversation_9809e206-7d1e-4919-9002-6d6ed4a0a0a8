import React, { useState } from 'react';
import { Plus, <PERSON>ting<PERSON>, Eye, EyeOff, GripVertical } from 'lucide-react';
import { useResumeStore } from '../../store/resumeStore';
import PersonalInfoForm from '../Forms/PersonalInfoForm';
import ExperienceForm from '../Forms/ExperienceForm';
import EducationForm from '../Forms/EducationForm';
import SkillsForm from '../Forms/SkillsForm';
import ProjectsForm from '../Forms/ProjectsForm';
import PublicationsForm from '../Forms/PublicationsForm';

const Sidebar: React.FC = () => {
  const { currentResume, toggleSectionVisibility, addSection } = useResumeStore();
  const [activeSection, setActiveSection] = useState<string>('personal');

  if (!currentResume) return null;

  const renderForm = () => {
    switch (activeSection) {
      case 'personal':
        return <PersonalInfoForm />;
      case 'experience':
        return <ExperienceForm />;
      case 'education':
        return <EducationForm />;
      case 'skills':
        return <SkillsForm />;
      case 'projects':
        return <ProjectsForm />;
      case 'publications':
        return <PublicationsForm />;
      default:
        return <div className="p-4 text-gray-500">Select a section to edit</div>;
    }
  };

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Edit Resume</h2>
        
        <nav className="space-y-1">
          <button
            onClick={() => setActiveSection('personal')}
            className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activeSection === 'personal'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            Personal Information
          </button>
          
          {currentResume.sections.map((section) => (
            <div key={section.id} className="flex items-center">
              <GripVertical className="w-4 h-4 text-gray-400 mr-2" />
              <button
                onClick={() => setActiveSection(section.type)}
                className={`flex-1 text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeSection === section.type
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {section.title}
              </button>
              <button
                onClick={() => toggleSectionVisibility(section.id)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title={section.visible ? 'Hide section' : 'Show section'}
              >
                {section.visible ? (
                  <Eye className="w-4 h-4" />
                ) : (
                  <EyeOff className="w-4 h-4" />
                )}
              </button>
            </div>
          ))}
        </nav>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              const sectionType = prompt('Enter section type (experience, education, skills, projects, publications):');
              const sectionTitle = prompt('Enter section title:');
              if (sectionType && sectionTitle) {
                addSection(sectionType as any, sectionTitle);
              }
            }}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add Section</span>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {renderForm()}
      </div>
    </div>
  );
};

export default Sidebar;
import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Zap, 
  Folder, 
  BookOpen,
  Award,
  Trophy,
  Globe,
  Heart,
  Star,
  Gamepad2,
  Users,
  Plus
} from 'lucide-react';
import { SectionType, SECTION_CONFIGS } from '../../types/templateDesigner';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';

const SECTION_ICONS: Record<SectionType, React.ComponentType<any>> = {
  personal: User,
  experience: Briefcase,
  education: GraduationCap,
  skills: Zap,
  projects: Folder,
  publications: BookOpen,
  certifications: Award,
  awards: Trophy,
  languages: Globe,
  volunteer: Heart,
  interests: Star,
  hobbies: Gamepad2,
  references: Users,
  custom: Plus
};

interface DraggableSectionItemProps {
  sectionType: SectionType;
}

const DraggableSectionItem: React.FC<DraggableSectionItemProps> = ({ sectionType }) => {
  const config = SECTION_CONFIGS[sectionType];
  const Icon = SECTION_ICONS[sectionType];

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `palette-${sectionType}`,
    data: {
      type: 'palette-section',
      sectionType
    }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.5 : 1,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`p-4 border border-gray-200 rounded-lg cursor-grab active:cursor-grabbing transition-all duration-200 hover:border-blue-300 hover:shadow-md ${
        isDragging ? 'shadow-lg z-50' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <Icon className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {config.defaultTitle}
          </h4>
          <p className="text-xs text-gray-500 truncate">
            {config.description}
          </p>
        </div>
      </div>
    </div>
  );
};

const SectionPalette: React.FC = () => {
  const { currentLayout } = useTemplateDesignerStore();

  if (!currentLayout) {
    return (
      <div className="p-4 text-center text-gray-500">
        <div className="text-sm">No layout loaded</div>
      </div>
    );
  }

  // Group sections by category
  const sectionCategories = {
    'Essential': ['personal', 'experience', 'education', 'skills'] as SectionType[],
    'Professional': ['projects', 'publications', 'certifications', 'awards'] as SectionType[],
    'Personal': ['languages', 'volunteer', 'interests', 'hobbies'] as SectionType[],
    'Other': ['references', 'custom'] as SectionType[]
  };

  return (
    <div className="p-4 space-y-6">
      <div>
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Section Library</h3>
        <p className="text-xs text-gray-600 mb-4">
          Drag sections to add them to your template layout
        </p>
      </div>

      {Object.entries(sectionCategories).map(([category, sections]) => (
        <div key={category}>
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide mb-3">
            {category}
          </h4>
          <div className="space-y-2">
            {sections.map(sectionType => (
              <DraggableSectionItem
                key={sectionType}
                sectionType={sectionType}
              />
            ))}
          </div>
        </div>
      ))}

      <div className="pt-4 border-t border-gray-200">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
            </div>
            <div>
              <h5 className="text-xs font-medium text-blue-900 mb-1">
                How to use
              </h5>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• Drag sections to layout zones</li>
                <li>• Reorder by dragging within zones</li>
                <li>• Click sections to customize</li>
                <li>• Use preview mode to test</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SectionPalette;

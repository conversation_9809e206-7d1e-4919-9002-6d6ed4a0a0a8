import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { 
  TemplateLayout, 
  TemplateSection, 
  LayoutZone, 
  SectionType,
  DEFAULT_LAYOUTS,
  SECTION_CONFIGS,
  DraggedSection,
  DropZoneInfo
} from '../types/templateDesigner';

interface TemplateDesignerStore {
  // State
  currentLayout: TemplateLayout | null;
  availableLayouts: TemplateLayout[];
  draggedSection: DraggedSection | null;
  dropZoneInfo: DropZoneInfo | null;
  selectedSection: TemplateSection | null;
  isDesignMode: boolean;
  previewMode: boolean;

  // Layout Management
  createNewLayout: (name: string, description: string) => void;
  loadLayout: (layoutId: string) => void;
  saveLayout: () => void;
  duplicateLayout: (layoutId: string) => void;
  deleteLayout: (layoutId: string) => void;
  updateLayoutInfo: (updates: Partial<TemplateLayout>) => void;

  // Zone Management
  addZone: (zone: Omit<LayoutZone, 'id'>) => void;
  updateZone: (zoneId: string, updates: Partial<LayoutZone>) => void;
  removeZone: (zoneId: string) => void;
  reorderZones: (zones: LayoutZone[]) => void;

  // Section Management
  addSection: (sectionType: SectionType, zoneId: string, position?: number) => void;
  updateSection: (sectionId: string, updates: Partial<TemplateSection>) => void;
  removeSection: (sectionId: string) => void;
  moveSection: (sectionId: string, targetZoneId: string, position: number) => void;
  duplicateSection: (sectionId: string) => void;

  // Drag and Drop
  startDrag: (section: TemplateSection, sourceZoneId: string) => void;
  endDrag: () => void;
  setDropZone: (dropZoneInfo: DropZoneInfo | null) => void;
  handleDrop: () => void;

  // Selection
  selectSection: (section: TemplateSection | null) => void;

  // Mode Management
  setDesignMode: (enabled: boolean) => void;
  setPreviewMode: (enabled: boolean) => void;

  // Utilities
  getSectionsByZone: (zoneId: string) => TemplateSection[];
  getZoneById: (zoneId: string) => LayoutZone | undefined;
  getSectionById: (sectionId: string) => TemplateSection | undefined;
  canDropInZone: (sectionType: SectionType, zoneId: string) => boolean;
}

export const useTemplateDesignerStore = create<TemplateDesignerStore>((set, get) => ({
  // Initial State
  currentLayout: null,
  availableLayouts: DEFAULT_LAYOUTS,
  draggedSection: null,
  dropZoneInfo: null,
  selectedSection: null,
  isDesignMode: false,
  previewMode: false,

  // Layout Management
  createNewLayout: (name: string, description: string) => {
    const newLayout: TemplateLayout = {
      id: uuidv4(),
      name,
      description,
      zones: [
        {
          id: uuidv4(),
          name: 'Header',
          type: 'header',
          width: '100%',
          sections: [],
          allowedSectionTypes: ['personal'],
          maxSections: 1
        },
        {
          id: uuidv4(),
          name: 'Main Content',
          type: 'main-content',
          width: '100%',
          sections: []
        }
      ],
      globalStyling: {
        fontFamily: 'Inter, sans-serif',
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        backgroundColor: '#ffffff',
        pageMargin: '2rem',
        sectionSpacing: '1.5rem'
      },
      isCustom: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    set(state => ({
      currentLayout: newLayout,
      availableLayouts: [...state.availableLayouts, newLayout]
    }));
  },

  loadLayout: (layoutId: string) => {
    const state = get();
    const layout = state.availableLayouts.find(l => l.id === layoutId);
    if (layout) {
      set({ currentLayout: { ...layout } });
    }
  },

  saveLayout: () => {
    const state = get();
    if (!state.currentLayout) return;

    const updatedLayout = {
      ...state.currentLayout,
      updatedAt: new Date()
    };

    set(state => ({
      currentLayout: updatedLayout,
      availableLayouts: state.availableLayouts.map(layout =>
        layout.id === updatedLayout.id ? updatedLayout : layout
      )
    }));
  },

  duplicateLayout: (layoutId: string) => {
    const state = get();
    const layout = state.availableLayouts.find(l => l.id === layoutId);
    if (!layout) return;

    const duplicatedLayout: TemplateLayout = {
      ...layout,
      id: uuidv4(),
      name: `${layout.name} (Copy)`,
      isCustom: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      zones: layout.zones.map(zone => ({
        ...zone,
        id: uuidv4(),
        sections: zone.sections.map(section => ({
          ...section,
          id: uuidv4(),
          zoneId: zone.id
        }))
      }))
    };

    set(state => ({
      availableLayouts: [...state.availableLayouts, duplicatedLayout]
    }));
  },

  deleteLayout: (layoutId: string) => {
    set(state => ({
      availableLayouts: state.availableLayouts.filter(layout => layout.id !== layoutId),
      currentLayout: state.currentLayout?.id === layoutId ? null : state.currentLayout
    }));
  },

  updateLayoutInfo: (updates: Partial<TemplateLayout>) => {
    set(state => ({
      currentLayout: state.currentLayout ? { ...state.currentLayout, ...updates } : null
    }));
  },

  // Zone Management
  addZone: (zoneData) => {
    const newZone: LayoutZone = {
      ...zoneData,
      id: uuidv4(),
      sections: []
    };

    set(state => ({
      currentLayout: state.currentLayout ? {
        ...state.currentLayout,
        zones: [...state.currentLayout.zones, newZone]
      } : null
    }));
  },

  updateZone: (zoneId: string, updates: Partial<LayoutZone>) => {
    set(state => ({
      currentLayout: state.currentLayout ? {
        ...state.currentLayout,
        zones: state.currentLayout.zones.map(zone =>
          zone.id === zoneId ? { ...zone, ...updates } : zone
        )
      } : null
    }));
  },

  removeZone: (zoneId: string) => {
    set(state => ({
      currentLayout: state.currentLayout ? {
        ...state.currentLayout,
        zones: state.currentLayout.zones.filter(zone => zone.id !== zoneId)
      } : null
    }));
  },

  reorderZones: (zones: LayoutZone[]) => {
    set(state => ({
      currentLayout: state.currentLayout ? {
        ...state.currentLayout,
        zones
      } : null
    }));
  },

  // Section Management
  addSection: (sectionType: SectionType, zoneId: string, position?: number) => {
    const config = SECTION_CONFIGS[sectionType];
    const newSection: TemplateSection = {
      id: uuidv4(),
      type: sectionType,
      title: config.defaultTitle,
      visible: true,
      order: position ?? 0,
      zoneId,
      styling: {
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderColor: '#e5e7eb',
        borderWidth: '1px',
        borderRadius: '8px',
        padding: '1rem',
        margin: '0.5rem 0',
        fontSize: '14px',
        fontWeight: 'normal',
        textAlign: 'left',
        showBorder: false,
        showBackground: false
      },
      content: {
        heading: {
          text: config.defaultTitle,
          style: 'h3',
          alignment: 'left',
          showUnderline: true,
          showIcon: false
        },
        bullets: {
          enabled: config.contentType === 'list',
          style: 'disc',
          indentation: '1rem',
          spacing: '0.25rem'
        },
        showDivider: false,
        dividerStyle: 'line'
      }
    };

    set(state => {
      if (!state.currentLayout) return state;

      const updatedZones = state.currentLayout.zones.map(zone => {
        if (zone.id === zoneId) {
          const sections = [...zone.sections];
          if (position !== undefined) {
            sections.splice(position, 0, newSection);
          } else {
            sections.push(newSection);
          }
          return { ...zone, sections };
        }
        return zone;
      });

      return {
        currentLayout: {
          ...state.currentLayout,
          zones: updatedZones
        }
      };
    });
  },

  updateSection: (sectionId: string, updates: Partial<TemplateSection>) => {
    set(state => {
      if (!state.currentLayout) return state;

      const updatedZones = state.currentLayout.zones.map(zone => ({
        ...zone,
        sections: zone.sections.map(section =>
          section.id === sectionId ? { ...section, ...updates } : section
        )
      }));

      return {
        currentLayout: {
          ...state.currentLayout,
          zones: updatedZones
        }
      };
    });
  },

  removeSection: (sectionId: string) => {
    set(state => {
      if (!state.currentLayout) return state;

      const updatedZones = state.currentLayout.zones.map(zone => ({
        ...zone,
        sections: zone.sections.filter(section => section.id !== sectionId)
      }));

      return {
        currentLayout: {
          ...state.currentLayout,
          zones: updatedZones
        },
        selectedSection: state.selectedSection?.id === sectionId ? null : state.selectedSection
      };
    });
  },

  moveSection: (sectionId: string, targetZoneId: string, position: number) => {
    set(state => {
      if (!state.currentLayout) return state;

      let sectionToMove: TemplateSection | null = null;
      
      // Remove section from current zone
      const updatedZones = state.currentLayout.zones.map(zone => {
        const sectionIndex = zone.sections.findIndex(s => s.id === sectionId);
        if (sectionIndex !== -1) {
          sectionToMove = { ...zone.sections[sectionIndex], zoneId: targetZoneId };
          return {
            ...zone,
            sections: zone.sections.filter(s => s.id !== sectionId)
          };
        }
        return zone;
      });

      // Add section to target zone
      if (sectionToMove) {
        const finalZones = updatedZones.map(zone => {
          if (zone.id === targetZoneId) {
            const sections = [...zone.sections];
            sections.splice(position, 0, sectionToMove!);
            return { ...zone, sections };
          }
          return zone;
        });

        return {
          currentLayout: {
            ...state.currentLayout,
            zones: finalZones
          }
        };
      }

      return state;
    });
  },

  duplicateSection: (sectionId: string) => {
    const state = get();
    const section = state.getSectionById(sectionId);
    if (!section) return;

    const duplicatedSection: TemplateSection = {
      ...section,
      id: uuidv4(),
      title: `${section.title} (Copy)`
    };

    set(state => {
      if (!state.currentLayout) return state;

      const updatedZones = state.currentLayout.zones.map(zone => {
        if (zone.id === section.zoneId) {
          const sectionIndex = zone.sections.findIndex(s => s.id === sectionId);
          const sections = [...zone.sections];
          sections.splice(sectionIndex + 1, 0, duplicatedSection);
          return { ...zone, sections };
        }
        return zone;
      });

      return {
        currentLayout: {
          ...state.currentLayout,
          zones: updatedZones
        }
      };
    });
  },

  // Drag and Drop
  startDrag: (section: TemplateSection, sourceZoneId: string) => {
    set({ draggedSection: { section, sourceZoneId } });
  },

  endDrag: () => {
    set({ draggedSection: null, dropZoneInfo: null });
  },

  setDropZone: (dropZoneInfo: DropZoneInfo | null) => {
    set({ dropZoneInfo });
  },

  handleDrop: () => {
    const state = get();
    if (!state.draggedSection || !state.dropZoneInfo || !state.dropZoneInfo.isValid) {
      state.endDrag();
      return;
    }

    state.moveSection(
      state.draggedSection.section.id,
      state.dropZoneInfo.zoneId,
      state.dropZoneInfo.position
    );
    state.endDrag();
  },

  // Selection
  selectSection: (section: TemplateSection | null) => {
    set({ selectedSection: section });
  },

  // Mode Management
  setDesignMode: (enabled: boolean) => {
    set({ isDesignMode: enabled, selectedSection: enabled ? null : get().selectedSection });
  },

  setPreviewMode: (enabled: boolean) => {
    set({ previewMode: enabled });
  },

  // Utilities
  getSectionsByZone: (zoneId: string) => {
    const state = get();
    const zone = state.currentLayout?.zones.find(z => z.id === zoneId);
    return zone?.sections || [];
  },

  getZoneById: (zoneId: string) => {
    const state = get();
    return state.currentLayout?.zones.find(z => z.id === zoneId);
  },

  getSectionById: (sectionId: string) => {
    const state = get();
    if (!state.currentLayout) return undefined;
    
    for (const zone of state.currentLayout.zones) {
      const section = zone.sections.find(s => s.id === sectionId);
      if (section) return section;
    }
    return undefined;
  },

  canDropInZone: (sectionType: SectionType, zoneId: string) => {
    const state = get();
    const zone = state.getZoneById(zoneId);
    if (!zone) return false;

    const config = SECTION_CONFIGS[sectionType];
    if (zone.allowedSectionTypes && !zone.allowedSectionTypes.includes(sectionType)) {
      return config.allowedZones.includes(zone.type);
    }

    if (zone.maxSections && zone.sections.length >= zone.maxSections) {
      return false;
    }

    return true;
  }
}));
}));

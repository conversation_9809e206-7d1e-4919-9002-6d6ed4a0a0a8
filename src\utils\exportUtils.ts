import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';
import { saveAs } from 'file-saver';
import { ResumeData } from '../types/resume';

export const exportToPDF = async (elementId: string, filename: string = 'resume.pdf') => {
  try {
    const element = document.getElementById(elementId);
    if (!element) throw new Error('Element not found');

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
      width: 794, // A4 width at 96 DPI
      height: 1123 // A4 height at 96 DPI
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    pdf.save(filename);
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    throw error;
  }
};

export const exportToWord = async (resumeData: ResumeData, filename: string = 'resume.docx') => {
  try {
    const children = [];

    // Add personal info
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: resumeData.personal.fullName,
            size: 32,
            bold: true
          })
        ],
        heading: HeadingLevel.TITLE
      })
    );

    if (resumeData.personal.email || resumeData.personal.phone) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `${resumeData.personal.email} | ${resumeData.personal.phone}`,
              size: 20
            })
          ]
        })
      );
    }

    if (resumeData.personal.address) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: resumeData.personal.address,
              size: 20
            })
          ]
        })
      );
    }

    // Add summary
    if (resumeData.personal.summary) {
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: 'Professional Summary',
              size: 24,
              bold: true
            })
          ],
          heading: HeadingLevel.HEADING_1
        })
      );

      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: resumeData.personal.summary,
              size: 20
            })
          ]
        })
      );
    }

    // Add sections
    resumeData.sections
      .filter(section => section.visible)
      .sort((a, b) => a.order - b.order)
      .forEach(section => {
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: section.title,
                size: 24,
                bold: true
              })
            ],
            heading: HeadingLevel.HEADING_1
          })
        );

        if (section.type === 'experience' && Array.isArray(section.data)) {
          section.data.forEach((exp: any) => {
            children.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${exp.position} at ${exp.company}`,
                    size: 22,
                    bold: true
                  })
                ]
              })
            );

            children.push(
              new Paragraph({
                children: [
                  new TextRun({
                    text: `${exp.startDate} - ${exp.current ? 'Present' : exp.endDate} | ${exp.location}`,
                    size: 20,
                    italics: true
                  })
                ]
              })
            );

            if (exp.description) {
              children.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: exp.description,
                      size: 20
                    })
                  ]
                })
              );
            }
          });
        }

        // Handle other section types similarly...
      });

    const doc = new Document({
      sections: [
        {
          properties: {},
          children
        }
      ]
    });

    const buffer = await Packer.toBuffer(doc);
    saveAs(new Blob([buffer]), filename);
  } catch (error) {
    console.error('Error exporting to Word:', error);
    throw error;
  }
};
import React, { useState, useCallback } from 'react';
import { GrammarError, grammarChecker } from '../../utils/grammarChecker';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface GrammarHighlighterProps {
  text: string;
  onChange: (text: string) => void;
  placeholder?: string;
  className?: string;
}

const GrammarHighlighter: React.FC<GrammarHighlighterProps> = ({
  text,
  onChange,
  placeholder = '',
  className = ''
}) => {
  const [errors, setErrors] = useState<GrammarError[]>([]);
  const [showSuggestions, setShowSuggestions] = useState<string | null>(null);

  const checkGrammar = useCallback((inputText: string) => {
    const grammarErrors = grammarChecker.checkText(inputText);
    setErrors(grammarErrors);
  }, []);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    onChange(newText);
    
    // Debounce grammar checking
    clearTimeout((window as any).grammarTimeout);
    (window as any).grammarTimeout = setTimeout(() => {
      checkGrammar(newText);
    }, 500);
  };

  const applySuggestion = (error: GrammarError, suggestion: string) => {
    const before = text.substring(0, error.offset);
    const after = text.substring(error.offset + error.length);
    const newText = before + suggestion + after;
    onChange(newText);
    setShowSuggestions(null);
    
    // Re-check after applying suggestion
    setTimeout(() => checkGrammar(newText), 100);
  };

  const renderTextWithHighlights = () => {
    if (!text || errors.length === 0) return text;

    const parts = [];
    let lastOffset = 0;

    // Sort errors by offset
    const sortedErrors = [...errors].sort((a, b) => a.offset - b.offset);

    sortedErrors.forEach((error, index) => {
      // Add text before error
      if (error.offset > lastOffset) {
        parts.push(text.substring(lastOffset, error.offset));
      }

      // Add highlighted error text
      parts.push(
        <span
          key={`error-${index}`}
          className={`relative cursor-pointer rounded px-1 ${
            error.type === 'spelling'
              ? 'bg-red-100 text-red-800 border-b-2 border-red-300'
              : error.type === 'grammar'
              ? 'bg-yellow-100 text-yellow-800 border-b-2 border-yellow-300'
              : 'bg-blue-100 text-blue-800 border-b-2 border-blue-300'
          }`}
          onClick={() => setShowSuggestions(showSuggestions === error.id ? null : error.id)}
        >
          {text.substring(error.offset, error.offset + error.length)}
          {showSuggestions === error.id && (
            <div className="absolute top-full left-0 z-10 mt-1 min-w-48 bg-white border border-gray-200 rounded-lg shadow-lg">
              <div className="p-3">
                <p className="text-sm text-gray-600 mb-2">{error.message}</p>
                {error.suggestions.length > 0 && (
                  <div className="space-y-1">
                    {error.suggestions.map((suggestion, idx) => (
                      <button
                        key={idx}
                        className="block w-full text-left px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
                        onClick={() => applySuggestion(error, suggestion)}
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </span>
      );

      lastOffset = error.offset + error.length;
    });

    // Add remaining text
    if (lastOffset < text.length) {
      parts.push(text.substring(lastOffset));
    }

    return parts;
  };

  return (
    <div className="relative">
      <div className="relative">
        <textarea
          value={text}
          onChange={handleTextChange}
          placeholder={placeholder}
          className={`w-full resize-none border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${className}`}
          style={{ 
            background: 'transparent',
            color: 'transparent',
            caretColor: 'black'
          }}
        />
        <div
          className="absolute inset-0 px-3 py-2 pointer-events-none whitespace-pre-wrap break-words"
          style={{
            font: 'inherit',
            lineHeight: 'inherit',
            letterSpacing: 'inherit'
          }}
        >
          {renderTextWithHighlights()}
        </div>
      </div>
      
      {errors.length > 0 && (
        <div className="flex items-center mt-2 text-sm">
          <AlertCircle className="w-4 h-4 text-yellow-500 mr-1" />
          <span className="text-gray-600">
            {errors.length} issue{errors.length !== 1 ? 's' : ''} found
          </span>
        </div>
      )}
      
      {errors.length === 0 && text.length > 0 && (
        <div className="flex items-center mt-2 text-sm">
          <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
          <span className="text-gray-600">No issues found</span>
        </div>
      )}
    </div>
  );
};

export default GrammarHighlighter;
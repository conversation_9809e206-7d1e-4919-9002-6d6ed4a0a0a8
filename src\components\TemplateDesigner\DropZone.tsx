import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';

interface DropZoneProps {
  zoneId: string;
  position: number;
  isFirst?: boolean;
  isLast?: boolean;
}

const DropZone: React.FC<DropZoneProps> = ({ zoneId, position, isFirst, isLast }) => {
  const { draggedSection, canDropInZone } = useTemplateDesignerStore();

  const {
    isOver,
    setNodeRef
  } = useDroppable({
    id: `dropzone-${zoneId}-${position}`,
    data: {
      type: 'dropzone',
      zoneId,
      position,
      isValid: draggedSection ? canDropInZone(draggedSection.section.type, zoneId) : false
    }
  });

  // Only show drop zone when dragging
  if (!draggedSection) {
    return null;
  }

  const canDrop = canDropInZone(draggedSection.section.type, zoneId);

  const getDropZoneStyles = () => {
    const baseStyles = "h-2 transition-all duration-200 rounded";
    
    if (isOver && canDrop) {
      return `${baseStyles} bg-blue-400 h-4`;
    } else if (isOver && !canDrop) {
      return `${baseStyles} bg-red-400 h-4`;
    } else {
      return `${baseStyles} bg-transparent hover:bg-gray-200`;
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={getDropZoneStyles()}
      style={{
        marginTop: isFirst ? 0 : '0.25rem',
        marginBottom: isLast ? 0 : '0.25rem'
      }}
    >
      {isOver && (
        <div className="flex items-center justify-center h-full">
          <div className={`text-xs font-medium ${
            canDrop ? 'text-blue-700' : 'text-red-700'
          }`}>
            {canDrop ? 'Drop here' : 'Cannot drop here'}
          </div>
        </div>
      )}
    </div>
  );
};

export default DropZone;

import React, { useState, useRef } from 'react';
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  Plus,
  Minus,
  Type,
  Palette
} from 'lucide-react';

interface RichContentEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  enableBullets?: boolean;
  bulletStyle?: 'disc' | 'circle' | 'square' | 'decimal' | 'custom';
  customBulletSymbol?: string;
}

const RichContentEditor: React.FC<RichContentEditorProps> = ({
  content,
  onChange,
  placeholder = 'Enter content...',
  enableBullets = true,
  bulletStyle = 'disc',
  customBulletSymbol = '→'
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [bulletItems, setBulletItems] = useState<string[]>(() => {
    if (enableBullets && content) {
      return content.split('\n').filter(line => line.trim());
    }
    return [''];
  });
  const editorRef = useRef<HTMLDivElement>(null);

  const handleBulletChange = (index: number, value: string) => {
    const newItems = [...bulletItems];
    newItems[index] = value;
    setBulletItems(newItems);
    
    if (enableBullets) {
      onChange(newItems.filter(item => item.trim()).join('\n'));
    }
  };

  const addBulletItem = () => {
    setBulletItems([...bulletItems, '']);
  };

  const removeBulletItem = (index: number) => {
    if (bulletItems.length > 1) {
      const newItems = bulletItems.filter((_, i) => i !== index);
      setBulletItems(newItems);
      
      if (enableBullets) {
        onChange(newItems.filter(item => item.trim()).join('\n'));
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const newItems = [...bulletItems];
      newItems.splice(index + 1, 0, '');
      setBulletItems(newItems);
    } else if (e.key === 'Backspace' && bulletItems[index] === '' && bulletItems.length > 1) {
      e.preventDefault();
      removeBulletItem(index);
    }
  };

  const getBulletSymbol = () => {
    switch (bulletStyle) {
      case 'disc':
        return '•';
      case 'circle':
        return '○';
      case 'square':
        return '■';
      case 'decimal':
        return (index: number) => `${index + 1}.`;
      case 'custom':
        return customBulletSymbol;
      default:
        return '•';
    }
  };

  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  if (enableBullets) {
    return (
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        {/* Toolbar */}
        <div className="bg-gray-50 border-b border-gray-200 px-3 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-700">Bullet Points</span>
            <div className="text-xs text-gray-500">
              Style: {bulletStyle === 'custom' ? customBulletSymbol : bulletStyle}
            </div>
          </div>
          <button
            onClick={addBulletItem}
            className="p-1 text-blue-600 hover:text-blue-700 transition-colors"
            title="Add bullet point"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>

        {/* Bullet Items */}
        <div className="p-3 space-y-2 max-h-64 overflow-y-auto">
          {bulletItems.map((item, index) => (
            <div key={index} className="flex items-start space-x-2 group">
              <div className="flex-shrink-0 w-6 text-center text-sm text-gray-600 mt-1">
                {typeof getBulletSymbol() === 'function' 
                  ? (getBulletSymbol() as Function)(index)
                  : getBulletSymbol()
                }
              </div>
              <input
                type="text"
                value={item}
                onChange={(e) => handleBulletChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                placeholder={index === 0 ? placeholder : 'Add another point...'}
                className="flex-1 text-sm border-none outline-none bg-transparent resize-none"
              />
              {bulletItems.length > 1 && (
                <button
                  onClick={() => removeBulletItem(index)}
                  className="flex-shrink-0 p-1 text-red-400 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-all"
                  title="Remove bullet point"
                >
                  <Minus className="w-3 h-3" />
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Rich text editor for non-bullet content
  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-200 px-3 py-2">
        <div className="flex items-center space-x-1">
          <button
            onClick={() => formatText('bold')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Bold"
          >
            <Bold className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => formatText('italic')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Italic"
          >
            <Italic className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => formatText('underline')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Underline"
          >
            <Underline className="w-4 h-4" />
          </button>

          <div className="w-px h-4 bg-gray-300 mx-1"></div>

          <button
            onClick={() => formatText('justifyLeft')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Align Left"
          >
            <AlignLeft className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => formatText('justifyCenter')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Align Center"
          >
            <AlignCenter className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => formatText('justifyRight')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Align Right"
          >
            <AlignRight className="w-4 h-4" />
          </button>

          <div className="w-px h-4 bg-gray-300 mx-1"></div>

          <button
            onClick={() => formatText('insertUnorderedList')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Bullet List"
          >
            <List className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => formatText('insertOrderedList')}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
            title="Numbered List"
          >
            <ListOrdered className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        onInput={(e) => onChange(e.currentTarget.innerHTML)}
        onFocus={() => setIsEditing(true)}
        onBlur={() => setIsEditing(false)}
        className={`p-3 min-h-24 text-sm outline-none ${
          isEditing ? 'bg-white' : 'bg-gray-50'
        }`}
        dangerouslySetInnerHTML={{ __html: content }}
        style={{ minHeight: '96px' }}
      />

      {!content && !isEditing && (
        <div className="absolute inset-0 p-3 text-sm text-gray-400 pointer-events-none">
          {placeholder}
        </div>
      )}
    </div>
  );
};

export default RichContentEditor;

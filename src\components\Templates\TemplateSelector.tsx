import React, { useState } from 'react';
import { Check, Eye, Palette, X, Edit, Trash2, Filter } from 'lucide-react';
import { useTemplateStore } from '../../store/templateStore';
import { useResumeStore } from '../../store/resumeStore';
import TemplateEditor from './TemplateEditor';

interface TemplateSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({ isOpen, onClose }) => {
  const { templates, selectedTemplate, setSelectedTemplate, deleteTemplate } = useTemplateStore();
  const { setTemplate } = useResumeStore();
  const [previewTemplate, setPreviewTemplate] = useState<string | null>(null);
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<'all' | 'built-in' | 'custom'>('all');

  if (!isOpen) return null;

  const filteredTemplates = templates.filter(template => {
    if (filterType === 'built-in') return !template.isCustom;
    if (filterType === 'custom') return template.isCustom;
    return true;
  });

  const handleSelectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId);
    setTemplate(templateId);
    onClose();
  };

  const handleEditTemplate = (templateId: string) => {
    setEditingTemplateId(templateId);
    setShowTemplateEditor(true);
  };

  const handleDeleteTemplate = (templateId: string) => {
    if (confirm('Are you sure you want to delete this template?')) {
      deleteTemplate(templateId);
    }
  };

  const handleCreateNew = () => {
    setEditingTemplateId(null);
    setShowTemplateEditor(true);
  };

  const getTemplatePreviewStyle = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (!template) return {};

    return {
      backgroundColor: template.styling.primaryColor,
      fontFamily: template.styling.fontFamily,
      borderRadius: template.styling.borderRadius,
    };
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Choose a Template</h2>
            <p className="text-gray-600 mt-1">Select a template that best fits your style and profession</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Filter Tabs */}
          <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
            {[
              { id: 'all', label: 'All Templates' },
              { id: 'built-in', label: 'Built-in' },
              { id: 'custom', label: 'Custom' }
            ].map(filter => (
              <button
                key={filter.id}
                onClick={() => setFilterType(filter.id as any)}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  filterType === filter.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <div
                key={template.id}
                className={`relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
                  selectedTemplate === template.id
                    ? 'border-blue-500 shadow-lg'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                }`}
                onClick={() => handleSelectTemplate(template.id)}
              >
                {/* Template Preview */}
                <div className="aspect-[3/4] bg-gray-50 relative overflow-hidden">
                  <div
                    className="w-full h-full p-4 text-white text-xs"
                    style={getTemplatePreviewStyle(template.id)}
                  >
                    {/* Mock resume content */}
                    <div className="space-y-2">
                      <div className="h-4 bg-white bg-opacity-90 rounded w-3/4"></div>
                      <div className="h-2 bg-white bg-opacity-70 rounded w-1/2"></div>
                      <div className="mt-4 space-y-1">
                        <div className="h-2 bg-white bg-opacity-60 rounded w-full"></div>
                        <div className="h-2 bg-white bg-opacity-60 rounded w-5/6"></div>
                        <div className="h-2 bg-white bg-opacity-60 rounded w-4/6"></div>
                      </div>
                      <div className="mt-4 space-y-1">
                        <div className="h-3 bg-white bg-opacity-80 rounded w-2/3"></div>
                        <div className="h-2 bg-white bg-opacity-60 rounded w-full"></div>
                        <div className="h-2 bg-white bg-opacity-60 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setPreviewTemplate(template.id);
                        }}
                        className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                        title="Preview"
                      >
                        <Eye className="w-4 h-4 text-gray-700" />
                      </button>
                      {template.isCustom && (
                        <>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditTemplate(template.id);
                            }}
                            className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                            title="Edit"
                          >
                            <Edit className="w-4 h-4 text-gray-700" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteTemplate(template.id);
                            }}
                            className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4 text-red-600" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Selected indicator */}
                  {selectedTemplate === template.id && (
                    <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                      <Check className="w-4 h-4" />
                    </div>
                  )}

                  {/* Custom template indicator */}
                  {template.isCustom && (
                    <div className="absolute top-2 left-2 bg-purple-500 text-white rounded-full p-1">
                      <Palette className="w-4 h-4" />
                    </div>
                  )}
                </div>

                {/* Template Info */}
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: template.styling.primaryColor }}
                      ></div>
                      <span className="text-xs text-gray-500">{template.styling.layout}</span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {template.sections.length} sections
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {filteredTemplates.length} of {templates.length} templates
            {filterType !== 'all' && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                {filterType}
              </span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateNew}
              className="px-4 py-2 bg-purple-600 text-white hover:bg-purple-700 rounded-md transition-colors"
            >
              Create Custom
            </button>
          </div>
        </div>
      </div>

      <TemplateEditor
        isOpen={showTemplateEditor}
        onClose={() => {
          setShowTemplateEditor(false);
          setEditingTemplateId(null);
        }}
        templateId={editingTemplateId || undefined}
      />
    </div>
  );
};

export default TemplateSelector;

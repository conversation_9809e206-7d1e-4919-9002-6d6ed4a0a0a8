import React, { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  GripVertical,
  Eye,
  EyeOff,
  Settings,
  Copy,
  Trash2,
  Edit3,
  User,
  Briefcase,
  GraduationCap,
  Zap,
  Folder,
  BookOpen,
  Award,
  Trophy,
  Globe,
  Heart,
  Star,
  Gamepad2,
  Users,
  Plus
} from 'lucide-react';
import { TemplateSection, SectionType } from '../../types/templateDesigner';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import SectionContentEditor from './SectionContentEditor';

const SECTION_ICONS: Record<SectionType, React.ComponentType<any>> = {
  personal: User,
  experience: Briefcase,
  education: GraduationCap,
  skills: Zap,
  projects: Folder,
  publications: BookOpen,
  certifications: Award,
  awards: Trophy,
  languages: Globe,
  volunteer: Heart,
  interests: Star,
  hobbies: Gamepad2,
  references: Users,
  custom: Plus
};

interface SectionItemProps {
  section: TemplateSection;
}

const SectionItem: React.FC<SectionItemProps> = ({ section }) => {
  const {
    selectedSection,
    previewMode,
    selectSection,
    updateSection,
    duplicateSection,
    removeSection,
    startDrag
  } = useTemplateDesignerStore();

  const [showContentEditor, setShowContentEditor] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: section.id,
    data: {
      type: 'section',
      section,
      sourceZoneId: section.zoneId
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const Icon = SECTION_ICONS[section.type];
  const isSelected = selectedSection?.id === section.id;

  const handleToggleVisibility = (e: React.MouseEvent) => {
    e.stopPropagation();
    updateSection(section.id, { visible: !section.visible });
  };

  const handleDuplicate = (e: React.MouseEvent) => {
    e.stopPropagation();
    duplicateSection(section.id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this section?')) {
      removeSection(section.id);
    }
  };

  const handleSelect = () => {
    if (!previewMode) {
      selectSection(section);
    }
  };

  const getSectionContent = () => {
    // This would render the actual section content based on type
    // For now, we'll show a placeholder
    const heading = section.content.heading;
    
    return (
      <div className="space-y-2">
        {heading && (
          <div 
            className={`font-medium ${
              heading.style === 'h1' ? 'text-xl' :
              heading.style === 'h2' ? 'text-lg' :
              heading.style === 'h3' ? 'text-base' :
              'text-sm'
            }`}
            style={{ 
              color: heading.color || section.styling.textColor,
              textAlign: heading.alignment || 'left'
            }}
          >
            {heading.showIcon && <Icon className="inline w-4 h-4 mr-2" />}
            {heading.text}
            {heading.showUnderline && (
              <div className="border-b border-current mt-1"></div>
            )}
          </div>
        )}
        
        {section.content.bullets?.enabled && (
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="w-2 h-2 bg-current rounded-full"></div>
              <span>Sample bullet point</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="w-2 h-2 bg-current rounded-full"></div>
              <span>Another bullet point</span>
            </div>
          </div>
        )}
        
        {!section.content.bullets?.enabled && (
          <div className="text-sm text-gray-600">
            Sample content for {section.title.toLowerCase()}
          </div>
        )}
        
        {section.content.showDivider && (
          <div className={`border-t ${
            section.content.dividerStyle === 'dots' ? 'border-dotted' :
            section.content.dividerStyle === 'dashes' ? 'border-dashed' :
            'border-solid'
          }`}></div>
        )}
      </div>
    );
  };

  if (previewMode) {
    return (
      <div
        style={{
          backgroundColor: section.styling.showBackground ? section.styling.backgroundColor : 'transparent',
          color: section.styling.textColor,
          padding: section.styling.padding,
          margin: section.styling.margin,
          borderRadius: section.styling.borderRadius,
          fontSize: section.styling.fontSize,
          fontWeight: section.styling.fontWeight,
          textAlign: section.styling.textAlign,
          border: section.styling.showBorder ? 
            `${section.styling.borderWidth} solid ${section.styling.borderColor}` : 
            'none'
        }}
        className={`${!section.visible ? 'opacity-50' : ''}`}
      >
        {getSectionContent()}
      </div>
    );
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      onClick={handleSelect}
      className={`group relative border rounded-lg transition-all duration-200 cursor-pointer ${
        isSelected 
          ? 'border-blue-500 bg-blue-50 shadow-md' 
          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
      } ${!section.visible ? 'opacity-60' : ''}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-2 p-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="w-4 h-4 text-gray-400" />
      </div>

      {/* Section Controls */}
      <div className="absolute right-2 top-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={handleToggleVisibility}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title={section.visible ? 'Hide section' : 'Show section'}
        >
          {section.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
        </button>

        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowContentEditor(true);
          }}
          className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
          title="Edit content"
        >
          <Edit3 className="w-4 h-4" />
        </button>

        <button
          onClick={handleSelect}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
          title="Edit properties"
        >
          <Settings className="w-4 h-4" />
        </button>

        <button
          onClick={handleDuplicate}
          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
          title="Duplicate section"
        >
          <Copy className="w-4 h-4" />
        </button>

        <button
          onClick={handleDelete}
          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
          title="Delete section"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>

      {/* Section Content */}
      <div className="p-4 pt-8">
        <div className="flex items-center space-x-2 mb-3">
          <Icon className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">
            {section.customTitle || section.title}
          </span>
          <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
            {section.type}
          </span>
        </div>
        
        {getSectionContent()}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none"></div>
      )}

      {/* Content Editor Modal */}
      <SectionContentEditor
        section={section}
        isOpen={showContentEditor}
        onClose={() => setShowContentEditor(false)}
      />
    </div>
  );
};

export default SectionItem;

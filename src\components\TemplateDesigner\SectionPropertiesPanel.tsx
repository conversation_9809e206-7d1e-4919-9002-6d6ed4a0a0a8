import React from 'react';
import { 
  <PERSON>, 
  Palette, 
  Layout, 
  List, 
  Minus,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Underline,
  Eye,
  EyeOff
} from 'lucide-react';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import { HeadingConfig, BulletConfig, SectionStyling } from '../../types/templateDesigner';

const SectionPropertiesPanel: React.FC = () => {
  const { selectedSection, updateSection } = useTemplateDesignerStore();

  if (!selectedSection) {
    return (
      <div className="p-4 text-center text-gray-500">
        <div className="text-sm">No section selected</div>
        <div className="text-xs mt-1">Click on a section to edit its properties</div>
      </div>
    );
  }

  const updateHeading = (updates: Partial<HeadingConfig>) => {
    updateSection(selectedSection.id, {
      content: {
        ...selectedSection.content,
        heading: {
          ...selectedSection.content.heading!,
          ...updates
        }
      }
    });
  };

  const updateBullets = (updates: Partial<BulletConfig>) => {
    updateSection(selectedSection.id, {
      content: {
        ...selectedSection.content,
        bullets: {
          ...selectedSection.content.bullets!,
          ...updates
        }
      }
    });
  };

  const updateStyling = (updates: Partial<SectionStyling>) => {
    updateSection(selectedSection.id, {
      styling: {
        ...selectedSection.styling,
        ...updates
      }
    });
  };

  const updateBasicInfo = (updates: Partial<typeof selectedSection>) => {
    updateSection(selectedSection.id, updates);
  };

  const heading = selectedSection.content.heading!;
  const bullets = selectedSection.content.bullets!;
  const styling = selectedSection.styling;

  return (
    <div className="p-4 space-y-6">
      <div>
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Section Properties</h3>
        <div className="text-xs text-gray-600 mb-4">
          Editing: {selectedSection.title}
        </div>
      </div>

      {/* Basic Information */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
          Basic Information
        </h4>
        
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Section Title
          </label>
          <input
            type="text"
            value={selectedSection.customTitle || selectedSection.title}
            onChange={(e) => updateBasicInfo({ customTitle: e.target.value })}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="visible"
            checked={selectedSection.visible}
            onChange={(e) => updateBasicInfo({ visible: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="visible" className="text-xs text-gray-700">
            Visible in resume
          </label>
        </div>
      </div>

      {/* Heading Configuration */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
          <Type className="w-3 h-3 mr-1" />
          Heading
        </h4>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Heading Text
          </label>
          <input
            type="text"
            value={heading.text}
            onChange={(e) => updateHeading({ text: e.target.value })}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Heading Style
          </label>
          <select
            value={heading.style}
            onChange={(e) => updateHeading({ style: e.target.value as any })}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="h1">H1 - Largest</option>
            <option value="h2">H2 - Large</option>
            <option value="h3">H3 - Medium</option>
            <option value="h4">H4 - Small</option>
            <option value="h5">H5 - Smaller</option>
            <option value="h6">H6 - Smallest</option>
          </select>
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Alignment
          </label>
          <div className="flex space-x-1">
            {[
              { value: 'left', icon: AlignLeft },
              { value: 'center', icon: AlignCenter },
              { value: 'right', icon: AlignRight }
            ].map(({ value, icon: Icon }) => (
              <button
                key={value}
                onClick={() => updateHeading({ alignment: value as any })}
                className={`flex-1 p-2 border rounded text-xs transition-colors ${
                  heading.alignment === value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 text-gray-600 hover:border-gray-400'
                }`}
              >
                <Icon className="w-3 h-3 mx-auto" />
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="showUnderline"
              checked={heading.showUnderline}
              onChange={(e) => updateHeading({ showUnderline: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="showUnderline" className="text-xs text-gray-700">
              Show underline
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="showIcon"
              checked={heading.showIcon}
              onChange={(e) => updateHeading({ showIcon: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="showIcon" className="text-xs text-gray-700">
              Show icon
            </label>
          </div>
        </div>
      </div>

      {/* Bullet Points Configuration */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
          <List className="w-3 h-3 mr-1" />
          Bullet Points
        </h4>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="enableBullets"
            checked={bullets.enabled}
            onChange={(e) => updateBullets({ enabled: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="enableBullets" className="text-xs text-gray-700">
            Enable bullet points
          </label>
        </div>

        {bullets.enabled && (
          <>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Bullet Style
              </label>
              <select
                value={bullets.style}
                onChange={(e) => updateBullets({ style: e.target.value as any })}
                className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="disc">• Disc</option>
                <option value="circle">○ Circle</option>
                <option value="square">■ Square</option>
                <option value="decimal">1. Numbers</option>
                <option value="none">None</option>
                <option value="custom">Custom</option>
              </select>
            </div>

            {bullets.style === 'custom' && (
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Custom Symbol
                </label>
                <input
                  type="text"
                  value={bullets.customSymbol || ''}
                  onChange={(e) => updateBullets({ customSymbol: e.target.value })}
                  placeholder="→"
                  className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            )}
          </>
        )}
      </div>

      {/* Styling */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
          <Palette className="w-3 h-3 mr-1" />
          Styling
        </h4>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Text Color
            </label>
            <input
              type="color"
              value={styling.textColor}
              onChange={(e) => updateStyling({ textColor: e.target.value })}
              className="w-full h-8 border border-gray-300 rounded cursor-pointer"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Background
            </label>
            <input
              type="color"
              value={styling.backgroundColor}
              onChange={(e) => updateStyling({ backgroundColor: e.target.value })}
              className="w-full h-8 border border-gray-300 rounded cursor-pointer"
            />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="showBackground"
              checked={styling.showBackground}
              onChange={(e) => updateStyling({ showBackground: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="showBackground" className="text-xs text-gray-700">
              Show background color
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="showBorder"
              checked={styling.showBorder}
              onChange={(e) => updateStyling({ showBorder: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="showBorder" className="text-xs text-gray-700">
              Show border
            </label>
          </div>
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 mb-1">
            Font Size
          </label>
          <select
            value={styling.fontSize}
            onChange={(e) => updateStyling({ fontSize: e.target.value })}
            className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="12px">12px - Small</option>
            <option value="14px">14px - Normal</option>
            <option value="16px">16px - Large</option>
            <option value="18px">18px - Larger</option>
          </select>
        </div>
      </div>

      {/* Divider */}
      <div className="space-y-4">
        <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide flex items-center">
          <Minus className="w-3 h-3 mr-1" />
          Divider
        </h4>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="showDivider"
            checked={selectedSection.content.showDivider}
            onChange={(e) => updateSection(selectedSection.id, {
              content: {
                ...selectedSection.content,
                showDivider: e.target.checked
              }
            })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="showDivider" className="text-xs text-gray-700">
            Show divider line
          </label>
        </div>

        {selectedSection.content.showDivider && (
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Divider Style
            </label>
            <select
              value={selectedSection.content.dividerStyle}
              onChange={(e) => updateSection(selectedSection.id, {
                content: {
                  ...selectedSection.content,
                  dividerStyle: e.target.value as any
                }
              })}
              className="w-full text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="line">Solid Line</option>
              <option value="dashes">Dashed Line</option>
              <option value="dots">Dotted Line</option>
            </select>
          </div>
        )}
      </div>
    </div>
  );
};

export default SectionPropertiesPanel;

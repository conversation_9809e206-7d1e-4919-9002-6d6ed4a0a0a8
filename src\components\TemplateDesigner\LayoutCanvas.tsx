import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useTemplateDesignerStore } from '../../store/templateDesignerStore';
import DropZone from './DropZone';
import SectionItem from './SectionItem';
import { LayoutZone } from '../../types/templateDesigner';

interface LayoutZoneComponentProps {
  zone: LayoutZone;
}

const LayoutZoneComponent: React.FC<LayoutZoneComponentProps> = ({ zone }) => {
  const { draggedSection, canDropInZone } = useTemplateDesignerStore();

  const {
    isOver,
    setNodeRef
  } = useDroppable({
    id: zone.id,
    data: {
      type: 'zone',
      zoneId: zone.id,
      accepts: zone.allowedSectionTypes || []
    }
  });

  const canDrop = draggedSection ? 
    canDropInZone(draggedSection.section.type, zone.id) : false;

  const getZoneStyles = () => {
    const baseStyles = "min-h-32 border-2 border-dashed transition-all duration-200 rounded-lg p-4";
    
    if (isOver && canDrop) {
      return `${baseStyles} border-blue-400 bg-blue-50`;
    } else if (isOver && !canDrop) {
      return `${baseStyles} border-red-400 bg-red-50`;
    } else if (draggedSection) {
      return `${baseStyles} border-gray-300 bg-gray-50`;
    } else {
      return `${baseStyles} border-gray-200 bg-white`;
    }
  };

  const getZoneLayoutStyles = () => {
    const styles: React.CSSProperties = {};
    
    switch (zone.type) {
      case 'header':
        styles.width = '100%';
        styles.marginBottom = '1rem';
        break;
      case 'footer':
        styles.width = '100%';
        styles.marginTop = '1rem';
        break;
      case 'left-sidebar':
        styles.width = zone.width || '30%';
        styles.marginRight = '1rem';
        break;
      case 'right-sidebar':
        styles.width = zone.width || '30%';
        styles.marginLeft = '1rem';
        break;
      case 'main-content':
        styles.flex = '1';
        break;
      case 'full-width':
        styles.width = '100%';
        styles.margin = '1rem 0';
        break;
    }
    
    return styles;
  };

  return (
    <div style={getZoneLayoutStyles()}>
      <div className="mb-2">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">{zone.name}</h3>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {zone.type}
          </span>
        </div>
        {zone.maxSections && (
          <div className="text-xs text-gray-500 mt-1">
            Max sections: {zone.maxSections}
          </div>
        )}
      </div>

      <div ref={setNodeRef} className={getZoneStyles()}>
        {zone.sections.length === 0 ? (
          <div className="flex items-center justify-center h-24 text-gray-400">
            <div className="text-center">
              <div className="text-sm font-medium">Drop sections here</div>
              <div className="text-xs mt-1">
                {zone.allowedSectionTypes ? 
                  `Accepts: ${zone.allowedSectionTypes.join(', ')}` : 
                  'Accepts any section'
                }
              </div>
            </div>
          </div>
        ) : (
          <SortableContext
            items={zone.sections.map(s => s.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {zone.sections.map((section, index) => (
                <React.Fragment key={section.id}>
                  <DropZone
                    zoneId={zone.id}
                    position={index}
                    isFirst={index === 0}
                  />
                  <SectionItem section={section} />
                </React.Fragment>
              ))}
              <DropZone
                zoneId={zone.id}
                position={zone.sections.length}
                isLast={true}
              />
            </div>
          </SortableContext>
        )}
      </div>
    </div>
  );
};

const LayoutCanvas: React.FC = () => {
  const { currentLayout, previewMode } = useTemplateDesignerStore();

  if (!currentLayout) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-gray-500">
          <div className="text-lg font-medium mb-2">No Layout Loaded</div>
          <div className="text-sm">Create or select a layout to start designing</div>
        </div>
      </div>
    );
  }

  const getLayoutStructure = () => {
    const zones = currentLayout.zones;
    const header = zones.find(z => z.type === 'header');
    const footer = zones.find(z => z.type === 'footer');
    const leftSidebar = zones.find(z => z.type === 'left-sidebar');
    const rightSidebar = zones.find(z => z.type === 'right-sidebar');
    const mainContent = zones.find(z => z.type === 'main-content');
    const fullWidth = zones.filter(z => z.type === 'full-width');

    return {
      header,
      footer,
      leftSidebar,
      rightSidebar,
      mainContent,
      fullWidth
    };
  };

  const { header, footer, leftSidebar, rightSidebar, mainContent, fullWidth } = getLayoutStructure();

  return (
    <div className="max-w-4xl mx-auto p-8">
      {/* Page Container */}
      <div 
        className={`bg-white shadow-lg rounded-lg overflow-hidden ${
          previewMode ? '' : 'border-2 border-dashed border-gray-300'
        }`}
        style={{
          minHeight: '11in',
          width: '8.5in',
          fontFamily: currentLayout.globalStyling.fontFamily,
          backgroundColor: currentLayout.globalStyling.backgroundColor,
          padding: currentLayout.globalStyling.pageMargin
        }}
      >
        {/* Header Zone */}
        {header && (
          <LayoutZoneComponent zone={header} />
        )}

        {/* Main Content Area */}
        <div className="flex">
          {/* Left Sidebar */}
          {leftSidebar && (
            <LayoutZoneComponent zone={leftSidebar} />
          )}

          {/* Main Content */}
          {mainContent && (
            <LayoutZoneComponent zone={mainContent} />
          )}

          {/* Right Sidebar */}
          {rightSidebar && (
            <LayoutZoneComponent zone={rightSidebar} />
          )}
        </div>

        {/* Full Width Zones */}
        {fullWidth.map(zone => (
          <LayoutZoneComponent key={zone.id} zone={zone} />
        ))}

        {/* Footer Zone */}
        {footer && (
          <LayoutZoneComponent zone={footer} />
        )}
      </div>

      {!previewMode && (
        <div className="mt-4 text-center text-sm text-gray-500">
          <div>Template: {currentLayout.name}</div>
          <div className="mt-1">
            Drag sections from the palette to build your layout
          </div>
        </div>
      )}
    </div>
  );
};

export default LayoutCanvas;

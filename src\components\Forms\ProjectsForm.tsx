import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, ExternalLink } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { useResumeStore } from '../../store/resumeStore';
import { Project } from '../../types/resume';
import GrammarHighlighter from '../GrammarChecker/GrammarHighlighter';

const ProjectsForm: React.FC = () => {
  const { currentResume, updateSection } = useResumeStore();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  
  const projectsSection = currentResume?.sections.find(s => s.type === 'projects');
  const projects = (projectsSection?.data || []) as Project[];

  const { register, handleSubmit, reset, setValue, watch } = useForm<Project>();
  const watchedDescription = watch('description');
  const watchedTechnologies = watch('technologies') || [];

  const handleSave = (data: any) => {
    const projectData = {
      ...data,
      technologies: typeof data.technologies === 'string' 
        ? data.technologies.split(',').map((t: string) => t.trim()).filter(Boolean)
        : data.technologies
    };

    const newProjects = editingId
      ? projects.map(proj => proj.id === editingId ? { ...projectData, id: editingId } : proj)
      : [...projects, { ...projectData, id: uuidv4() }];
    
    if (projectsSection) {
      updateSection(projectsSection.id, newProjects);
    }
    
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleEdit = (project: Project) => {
    reset({
      ...project,
      technologies: project.technologies?.join(', ') as any
    });
    setEditingId(project.id);
    setIsAdding(false);
  };

  const handleDelete = (id: string) => {
    const newProjects = projects.filter(proj => proj.id !== id);
    if (projectsSection) {
      updateSection(projectsSection.id, newProjects);
    }
  };

  const handleCancel = () => {
    reset();
    setEditingId(null);
    setIsAdding(false);
  };

  const handleDescriptionChange = (text: string) => {
    setValue('description', text);
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Projects</h3>
        <button
          onClick={() => setIsAdding(true)}
          className="flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:text-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add</span>
        </button>
      </div>

      {(isAdding || editingId) && (
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <form onSubmit={handleSubmit(handleSave)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Name *
              </label>
              <input
                {...register('name', { required: true })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <GrammarHighlighter
                text={watchedDescription || ''}
                onChange={handleDescriptionChange}
                placeholder="Describe what the project does and your role..."
                className="min-h-24"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Technologies Used
              </label>
              <input
                {...register('technologies')}
                placeholder="React, Node.js, MongoDB (comma-separated)"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project URL
              </label>
              <input
                {...register('url')}
                type="url"
                placeholder="https://github.com/username/project"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date *
                </label>
                <input
                  {...register('startDate', { required: true })}
                  type="month"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  {...register('endDate')}
                  type="month"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex space-x-2">
              <button
                type="submit"
                className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="flex items-center space-x-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-3">
        {projects.map((project) => (
          <div key={project.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-gray-900">{project.name}</h4>
                  {project.url && (
                    <a
                      href={project.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {project.startDate}{project.endDate ? ` - ${project.endDate}` : ' - Ongoing'}
                </p>
                {project.description && (
                  <p className="text-sm text-gray-700 mt-2">{project.description}</p>
                )}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              <div className="flex space-x-1 ml-4">
                <button
                  onClick={() => handleEdit(project)}
                  className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(project.id)}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectsForm;
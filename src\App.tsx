import React from 'react';
import { Toaster } from 'react-hot-toast';
import Header from './components/Layout/Header';
import Sidebar from './components/Sidebar/Sidebar';
import ResumePreview from './components/Resume/ResumePreview';
import { useResumeStore } from './store/resumeStore';
import { exportToPDF, exportToWord } from './utils/exportUtils';
import toast from 'react-hot-toast';

function App() {
  const { currentResume } = useResumeStore();

  const handleExportPDF = async () => {
    try {
      toast.loading('Generating PDF...', { id: 'pdf-export' });
      await exportToPDF('resume-preview', `${currentResume?.title || 'resume'}.pdf`);
      toast.success('PDF exported successfully!', { id: 'pdf-export' });
    } catch (error) {
      console.error('PDF export failed:', error);
      toast.error('Failed to export PDF. Please try again.', { id: 'pdf-export' });
    }
  };

  const handleExportWord = async () => {
    if (!currentResume) return;
    
    try {
      toast.loading('Generating Word document...', { id: 'word-export' });
      await exportToWord(currentResume, `${currentResume.title || 'resume'}.docx`);
      toast.success('Word document exported successfully!', { id: 'word-export' });
    } catch (error) {
      console.error('Word export failed:', error);
      toast.error('Failed to export Word document. Please try again.', { id: 'word-export' });
    }
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      <Header onExportPDF={handleExportPDF} onExportWord={handleExportWord} />

      <div className="flex-1 flex overflow-hidden">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="p-8 pb-4">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Resume Preview</h2>
                <p className="text-gray-600">
                  Use the sidebar to edit your resume. Changes will appear here in real-time.
                </p>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-auto px-8">
            <div className="max-w-4xl mx-auto">
              <div className="flex justify-center">
                <div className="shadow-2xl rounded-lg overflow-hidden">
                  <ResumePreview />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </div>
  );
}

export default App;